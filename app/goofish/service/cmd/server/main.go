package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/tx7do/go-utils/trans"
	"github.com/tx7do/kratos-bootstrap/bootstrap"

	"kratos-admin/app/goofish/service/internal/server"
)

var version string

// go build -ldflags "-X main.version=x.y.z"

func newApp(
	lg log.Logger,
	re registry.Registrar,
	hs *server.HTTPServer,
) *kratos.App {
	// 过滤掉 nil 服务器，只添加非 nil 的服务器
	var servers []kratos.Option

	if re != nil {
		servers = append(servers, kratos.Registrar(re))
	}

	if hs != nil {
		servers = append(servers, kratos.Server(hs))
	}

	return kratos.New(servers...)
}

func main() {
	bootstrap.Bootstrap(initApp, trans.Ptr("goofish"), trans.Ptr(version))
}
