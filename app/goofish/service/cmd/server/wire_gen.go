// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/tx7do/kratos-bootstrap/api/gen/go/conf/v1"
	"kratos-admin/app/goofish/service/internal/data"
	"kratos-admin/app/goofish/service/internal/server"
	"kratos-admin/app/goofish/service/internal/service"
)

// Injectors from wire.go:

// initApp init kratos application.
func initApp(logger log.Logger, registrar registry.Registrar, bootstrap *v1.Bootstrap) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(bootstrap, logger)
	if err != nil {
		return nil, nil, err
	}
	goofishService := service.NewGoofishService(dataData, logger)
	v := server.NewHTTPServer(bootstrap, logger, goofishService)
	app := newApp(logger, registrar, v)
	return app, func() {
		cleanup()
	}, nil
}
