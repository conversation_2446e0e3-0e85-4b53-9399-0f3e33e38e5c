data:
  database:
    driver: "sqlite3"
    # SQLite 数据库文件路径
    source: "file:./data/goofish.db?cache=shared&_fk=1"
    migrate: true
    debug: false
    enable_trace: false
    enable_metrics: false
    max_idle_connections: 25
    max_open_connections: 25
    connection_max_lifetime: 300s

  # redis:
  #   addr: "localhost:6379"
  #   password: ""
  #   dial_timeout: 10s
  #   read_timeout: 0.4s
  #   write_timeout: 0.6s
