server:
  rest:
    addr: ":8080"
    timeout: 10s
    enable_swagger: true
    enable_pprof: true
    cors:
      headers:
        - "X-Requested-With"
        - "Content-Type"
        - "Authorization"
        - "Accept"
        - "Origin"
        - "Cache-Control"
        - "X-File-Name"
        - "Accept-Language"
        - "Accept-Encoding"
      methods:
        - "GET"
        - "POST"
        - "PUT"
        - "DELETE"
        - "HEAD"
        - "OPTIONS"
        - "PATCH"
      origins:
        - "*"
      expose_headers:
        - "Content-Length"
        - "Access-Control-Allow-Origin"
        - "Access-Control-Allow-Headers"
        - "Cache-Control"
        - "Content-Language"
        - "Content-Type"
      allow_credentials: true
      max_age: 86400
    middleware:
      enable_logging: true
      enable_recovery: true
      enable_tracing: true
      enable_validate: true
      enable_circuit_breaker: true
      enable_metadata: true
      auth:
        method: "HS256"
        key: "some_api_key"
        access_token_expires: 0s
        refresh_token_expires: 0s
        access_token_key_prefix: "uat_"
        refresh_token_key_prefix: "urt_"
