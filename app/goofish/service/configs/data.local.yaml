data:
  database:
    driver: "sqlite3"
    # SQLite 数据库文件路径
    source: "file:./data/goofish.db?cache=shared&_fk=1"
    migrate: true
    debug: true
    enable_trace: false
    enable_metrics: false
    max_idle_connections: 25
    max_open_connections: 25
    connection_max_lifetime: 300s

  # 注释掉 Redis 配置，禁用 Redis 依赖
  # redis:
  #   addr: "localhost:6379"
  #   password: ""
  #   dial_timeout: 10s
  #   read_timeout: 0.4s
  #   write_timeout: 0.6s
