package middleware

import (
	"context"
	"errors"
	"os"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
)

func TestCodecErrorHandler(t *testing.T) {
	logger := log.NewStdLogger(os.Stdout)

	// 创建中间件
	handler := CodecErrorHandler(logger)

	// 测试正常情况
	t.Run("normal case", func(t *testing.T) {
		mockHandler := func(ctx context.Context, req interface{}) (interface{}, error) {
			return "success", nil
		}

		wrappedHandler := handler(mockHandler)
		result, err := wrappedHandler(context.Background(), "test")

		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}
		if result != "success" {
			t.<PERSON>rf("Expected 'success', got %v", result)
		}
	})

	// 测试 Content-Type 错误
	t.Run("content-type error", func(t *testing.T) {
		mockHandler := func(ctx context.Context, req interface{}) (interface{}, error) {
			return nil, errors.New("unregister Content-Type: application/json")
		}

		wrappedHandler := handler(mockHandler)
		result, err := wrappedHandler(context.Background(), "test")

		if err == nil {
			t.Error("Expected error, got nil")
		}
		if result != nil {
			t.Errorf("Expected nil result, got %v", result)
		}

		// 检查错误消息
		if !isContentTypeError(err) {
			t.Errorf("Expected Content-Type error, got %v", err)
		}
	})

	// 测试其他错误
	t.Run("other error", func(t *testing.T) {
		originalErr := errors.New("some other error")
		mockHandler := func(ctx context.Context, req interface{}) (interface{}, error) {
			return nil, originalErr
		}

		wrappedHandler := handler(mockHandler)
		result, err := wrappedHandler(context.Background(), "test")

		if err != originalErr {
			t.Errorf("Expected original error %v, got %v", originalErr, err)
		}
		if result != nil {
			t.Errorf("Expected nil result, got %v", result)
		}
	})
}

func TestIsContentTypeError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{"nil error", nil, false},
		{"content-type error", errors.New("unregister Content-Type: application/json"), true},
		{"codec error", errors.New("codec error occurred"), true},
		{"unregister error", errors.New("unregister something"), true},
		{"unsupported media type", errors.New("unsupported media type"), true},
		{"other error", errors.New("database connection failed"), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isContentTypeError(tt.err)
			if result != tt.expected {
				t.Errorf("isContentTypeError(%v) = %v, want %v", tt.err, result, tt.expected)
			}
		})
	}
}

func TestGetContentTypeFromError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected string
	}{
		{"nil error", nil, ""},
		{"with content-type", errors.New("unregister Content-Type: application/json"), "application/json"},
		{"with content-type and space", errors.New("unregister Content-Type: text/html charset=utf-8"), "text/html"},
		{"no content-type", errors.New("some other error"), ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getContentTypeFromError(tt.err)
			if result != tt.expected {
				t.Errorf("getContentTypeFromError(%v) = %v, want %v", tt.err, result, tt.expected)
			}
		})
	}
}

func TestRecoveryHandler(t *testing.T) {
	logger := log.NewStdLogger(os.Stdout)

	// 创建中间件
	handler := RecoveryHandler(logger)

	// 测试正常情况
	t.Run("normal case", func(t *testing.T) {
		mockHandler := func(ctx context.Context, req interface{}) (interface{}, error) {
			return "success", nil
		}

		wrappedHandler := handler(mockHandler)
		result, err := wrappedHandler(context.Background(), "test")

		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}
		if result != "success" {
			t.Errorf("Expected 'success', got %v", result)
		}
	})

	// 测试 panic 恢复
	t.Run("panic recovery", func(t *testing.T) {
		mockHandler := func(ctx context.Context, req interface{}) (interface{}, error) {
			panic("test panic")
		}

		wrappedHandler := handler(mockHandler)
		result, err := wrappedHandler(context.Background(), "test")

		if err == nil {
			t.Error("Expected error after panic, got nil")
		}
		if result != nil {
			t.Errorf("Expected nil result after panic, got %v", result)
		}
	})
}
