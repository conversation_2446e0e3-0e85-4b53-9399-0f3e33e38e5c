package middleware

import (
	"context"
	"strings"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
)

// CodecErrorHandler 处理编解码器相关错误的中间件
func CodecErrorHandler(logger log.Logger) middleware.Middleware {
	return middleware.Middleware(func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// 执行请求处理
			reply, err := handler(ctx, req)

			// 如果没有错误，直接返回
			if err == nil {
				return reply, nil
			}

			// 检查是否是 Content-Type 相关的错误
			if isContentTypeError(err) {
				log.NewHelper(logger).WithContext(ctx).Errorf("CODEC error detected: %v", err)

				// 返回标准化的 CODEC 错误
				contentType := getContentTypeFromError(err)
				message := "unregister Content-Type: " + contentType
				return nil, errors.BadRequest("CODEC", message)
			}

			// 其他错误直接返回
			return reply, err
		}
	})
}

// isContentTypeError 检查错误是否与 Content-Type 相关
func isContentTypeError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := err.Error()
	return strings.Contains(errMsg, "Content-Type") ||
		strings.Contains(errMsg, "codec") ||
		strings.Contains(errMsg, "unregister") ||
		strings.Contains(errMsg, "unsupported media type") ||
		strings.Contains(errMsg, "invalid content type")
}

// getContentTypeFromError 从错误中提取 Content-Type 信息
func getContentTypeFromError(err error) string {
	if err == nil {
		return ""
	}

	errMsg := err.Error()

	// 尝试从错误消息中提取 Content-Type
	if strings.Contains(errMsg, "Content-Type:") {
		parts := strings.Split(errMsg, "Content-Type:")
		if len(parts) > 1 {
			contentType := strings.TrimSpace(parts[1])
			// 取第一个空格或换行符之前的内容
			if idx := strings.IndexAny(contentType, " \n\r\t"); idx > 0 {
				contentType = contentType[:idx]
			}
			return contentType
		}
	}

	return ""
}

// RecoveryHandler 处理 panic 并转换为 CODEC 错误
func RecoveryHandler(logger log.Logger) middleware.Middleware {
	return middleware.Middleware(func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			defer func() {
				if r := recover(); r != nil {
					log.NewHelper(logger).WithContext(ctx).Errorf("Panic recovered: %v", r)

					// 检查是否是编解码器相关的 panic
					if panicMsg, ok := r.(string); ok && isContentTypeError(&codecError{msg: panicMsg}) {
						contentType := getContentTypeFromError(&codecError{msg: panicMsg})
						message := "unregister Content-Type: " + contentType
						err = errors.BadRequest("CODEC", message)
						return
					}

					// 其他 panic 转换为内部服务器错误
					err = errors.InternalServer("INTERNAL_ERROR", "internal server error")
				}
			}()

			return handler(ctx, req)
		}
	})
}

// codecError 用于包装错误消息
type codecError struct {
	msg string
}

func (e *codecError) Error() string {
	return e.msg
}
