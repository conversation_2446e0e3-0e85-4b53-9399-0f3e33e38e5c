package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/tx7do/kratos-bootstrap/rpc"

	conf "github.com/tx7do/kratos-bootstrap/api/gen/go/conf/v1"

	goofishV1 "kratos-admin/api/gen/go/goofish/service/v1"
	codecMiddleware "kratos-admin/app/goofish/service/internal/middleware"
	"kratos-admin/app/goofish/service/internal/service"
)

type HTTPServer = http.Server

// NewHTTPServer new an HTTP server.
func NewHTTPServer(
	cfg *conf.Bootstrap,
	logger log.Logger,
	goofishSvc *service.GoofishService,
) *HTTPServer {
	// 创建中间件
	middlewares := []middleware.Middleware{
		codecMiddleware.Recovery<PERSON><PERSON><PERSON>(logger),
		codecMiddleware.CodecError<PERSON>and<PERSON>(logger),
	}

	srv := rpc.CreateRestServer(cfg, middlewares...)

	// 注册 goofish HTTP 服务
	goofishV1.RegisterGoofishHttpServiceHTTPServer(srv, goofishSvc)

	return srv
}
