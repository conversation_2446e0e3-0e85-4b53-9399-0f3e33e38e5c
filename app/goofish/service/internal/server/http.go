package server

import (
	"encoding/json"
	"strings"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport/http"

	conf "github.com/tx7do/kratos-bootstrap/api/gen/go/conf/v1"

	goofishV1 "kratos-admin/api/gen/go/goofish/service/v1"
	codecMiddleware "kratos-admin/app/goofish/service/internal/middleware"
	"kratos-admin/app/goofish/service/internal/service"
)

type HTTPServer = http.Server

// codecErrorEncoder 自定义错误编码器，处理 CODEC 错误
func codecErrorEncoder(w http.ResponseWriter, r *http.Request, err error) {
	se := errors.FromError(err)

	// 检查是否是 CODEC 错误且消息为空的情况
	if se.Reason == "CODEC" && strings.TrimSpace(se.Message) == "unregister Content-Type:" {
		// 获取请求的 Content-Type
		contentType := r.Header.Get("Content-Type")
		if contentType == "" {
			contentType = "empty"
		}

		// 创建新的错误消息
		message := "unregister Content-Type: " + contentType
		newErr := errors.BadRequest("CODEC", message)
		se = errors.FromError(newErr)
	}

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(int(se.Code))

	// 构造响应体
	response := map[string]interface{}{
		"code":     se.Code,
		"reason":   se.Reason,
		"message":  se.Message,
		"metadata": se.Metadata,
	}

	json.NewEncoder(w).Encode(response)
}

// NewHTTPServer new an HTTP server.
func NewHTTPServer(
	cfg *conf.Bootstrap,
	logger log.Logger,
	goofishSvc *service.GoofishService,
) *HTTPServer {
	// 创建中间件
	middlewares := []middleware.Middleware{
		codecMiddleware.RecoveryHandler(logger),
		codecMiddleware.CodecErrorHandler(logger),
	}

	// 创建带有自定义错误编码器的服务器选项
	opts := []http.ServerOption{
		http.ErrorEncoder(codecErrorEncoder),
		http.Middleware(middlewares...),
	}

	// 从配置中获取服务器地址和其他选项
	if cfg != nil && cfg.Server != nil && cfg.Server.Rest != nil {
		if addr := cfg.Server.Rest.GetAddr(); addr != "" {
			opts = append(opts, http.Address(addr))
		}
		if timeout := cfg.Server.Rest.GetTimeout(); timeout != nil {
			opts = append(opts, http.Timeout(timeout.AsDuration()))
		}
	}

	// 直接创建 Kratos HTTP 服务器
	srv := http.NewServer(opts...)

	// 注册 goofish HTTP 服务
	goofishV1.RegisterGoofishHttpServiceHTTPServer(srv, goofishSvc)

	return srv
}
