package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	goofishV1 "kratos-admin/api/gen/go/goofish/service/v1"
	"kratos-admin/app/goofish/service/internal/data"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(
	NewGoofishService,
)

type GoofishService struct {
	goofishV1.GoofishServiceServer
	goofishV1.GoofishHttpServiceHTTPServer
	log *log.Helper
}

func NewGoofishService(data *data.Data, logger log.Logger) *GoofishService {
	l := log.NewHelper(log.With(logger, "module", "service/goofish-service"))
	return &GoofishService{
		log: l,
	}
}

// GetPlatformInfo 查询平台信息
func (s *GoofishService) GetPlatformInfo(ctx context.Context, req *goofishV1.PlatformInfoRequest) (*goofishV1.PlatformInfoResponse, error) {
	s.log.WithContext(ctx).Info("GetPlatformInfo called")
	return &goofishV1.PlatformInfoResponse{
		Code: 0,
		Msg:  "success",
		Data: &goofishV1.PlatformInfoData{
			AppId: 123456,
		},
	}, nil
}

// GetUserInfo 查询商户信息
func (s *GoofishService) GetUserInfo(ctx context.Context, req *goofishV1.UserInfoRequest) (*goofishV1.UserInfoResponse, error) {
	s.log.WithContext(ctx).Info("GetUserInfo called")
	return &goofishV1.UserInfoResponse{
		Code: 0,
		Msg:  "success",
		Data: &goofishV1.UserInfoData{
			Balance: 10000,
		},
	}, nil
}

// GetGoodsDetail 查询商品详情
func (s *GoofishService) GetGoodsDetail(ctx context.Context, req *goofishV1.GoodsDetailRequest) (*goofishV1.GoodsDetailResponse, error) {
	s.log.WithContext(ctx).Info("GetGoodsDetail called")
	return &goofishV1.GoodsDetailResponse{
		Code: 0,
		Msg:  "success",
		Data: &goofishV1.GoodsDetail{
			GoodsNo:   req.GoodsNo,
			GoodsType: req.GoodsType,
			GoodsName: "测试商品",
			Price:     1000,
			Stock:     100,
			Status:    1,
		},
	}, nil
}

// GetGoodsList 查询商品列表
func (s *GoofishService) GetGoodsList(ctx context.Context, req *goofishV1.GoodsListRequest) (*goofishV1.GoodsListResponse, error) {
	s.log.WithContext(ctx).Info("GetGoodsList called")
	return &goofishV1.GoodsListResponse{
		Code: 0,
		Msg:  "success",
		Data: &goofishV1.GoodsListData{
			List: []*goofishV1.GoodsDetail{
				{
					GoodsNo:   "GOODS001",
					GoodsType: 1,
					GoodsName: "测试商品1",
					Price:     1000,
					Stock:     100,
					Status:    1,
				},
			},
			Count: 1,
		},
	}, nil
}

// GetGoodsChangeSubscribeList 查询商品订阅列表
func (s *GoofishService) GetGoodsChangeSubscribeList(ctx context.Context, req *goofishV1.GoodsChangeSubscribeListRequest) (*goofishV1.GoodsChangeSubscribeListResponse, error) {
	s.log.WithContext(ctx).Info("GetGoodsChangeSubscribeList called")
	return &goofishV1.GoodsChangeSubscribeListResponse{
		Code: 0,
		Msg:  "success",
		Data: &goofishV1.GoodsChangeSubscribeListData{
			List:  []*goofishV1.GoodsChangeSubscribeListItem{},
			Count: 0,
		},
	}, nil
}

// GoodsChangeSubscribe 订阅商品变更通知
func (s *GoofishService) GoodsChangeSubscribe(ctx context.Context, req *goofishV1.GoodsChangeSubscribeRequest) (*goofishV1.GoodsChangeSubscribeResponse, error) {
	s.log.WithContext(ctx).Info("GoodsChangeSubscribe called")
	return &goofishV1.GoodsChangeSubscribeResponse{
		Code: 0,
		Msg:  "订阅成功",
	}, nil
}

// GoodsChangeUnsubscribe 取消商品变更通知
func (s *GoofishService) GoodsChangeUnsubscribe(ctx context.Context, req *goofishV1.GoodsChangeUnsubscribeRequest) (*goofishV1.GoodsChangeUnsubscribeResponse, error) {
	s.log.WithContext(ctx).Info("GoodsChangeUnsubscribe called")
	return &goofishV1.GoodsChangeUnsubscribeResponse{
		Code: 0,
		Msg:  "取消订阅成功",
	}, nil
}

// GoodsCallback 商品回调通知
func (s *GoofishService) GoodsCallback(ctx context.Context, req *goofishV1.GoodsCallbackRequest) (*goofishV1.GoodsCallbackResponse, error) {
	s.log.WithContext(ctx).Info("GoodsCallback called")
	return &goofishV1.GoodsCallbackResponse{
		Code: 0,
		Msg:  "回调处理成功",
	}, nil
}

// CreateRechargeOrder 创建直充订单
func (s *GoofishService) CreateRechargeOrder(ctx context.Context, req *goofishV1.CreateRechargeOrderRequest) (*goofishV1.CreateRechargeOrderResponse, error) {
	s.log.WithContext(ctx).Info("CreateRechargeOrder called")
	return &goofishV1.CreateRechargeOrderResponse{
		Code: 0,
		Msg:  "订单创建成功",
		Data: &goofishV1.RechargeOrderData{
			OrderNo:     "ORDER123456",
			OutOrderNo:  req.BizOrderNo,
			OrderStatus: 1,
			OrderAmount: req.MaxAmount,
			GoodsName:   "测试商品",
			OrderTime:   **********,
			EndTime:     ********** + 3600,
			Remark:      "直充订单创建成功",
		},
	}, nil
}

// CreateCardOrder 创建卡密订单
func (s *GoofishService) CreateCardOrder(ctx context.Context, req *goofishV1.CreateCardOrderRequest) (*goofishV1.CreateCardOrderResponse, error) {
	s.log.WithContext(ctx).Info("CreateCardOrder called")
	return &goofishV1.CreateCardOrderResponse{
		Code: 0,
		Msg:  "卡密订单创建成功",
		Data: &goofishV1.CardOrderData{
			OrderNo:     "CARD123456",
			OutOrderNo:  req.BizOrderNo,
			OrderStatus: 1,
			OrderAmount: req.MaxAmount,
			OrderTime:   **********,
			EndTime:     ********** + 3600,
			CardItems: []*goofishV1.CardItem{
				{
					CardNo:  "CARD001",
					CardPwd: "PWD001",
				},
			},
			Remark: "卡密订单创建成功",
		},
	}, nil
}

// GetOrderDetail 查询订单详情
func (s *GoofishService) GetOrderDetail(ctx context.Context, req *goofishV1.OrderDetailRequest) (*goofishV1.OrderDetailResponse, error) {
	s.log.WithContext(ctx).Info("GetOrderDetail called")
	return &goofishV1.OrderDetailResponse{
		Code: 0,
		Msg:  "success",
		Data: &goofishV1.OrderDetailData{
			OrderNo:     req.OrderNo,
			OutOrderNo:  "OUT123456",
			OrderStatus: 1,
			OrderAmount: 1000,
			GoodsName:   "测试商品",
			OrderTime:   **********,
			EndTime:     ********** + 3600,
			BizContent: &goofishV1.BizContent{
				Account:    "test_account",
				GameName:   "测试游戏",
				GameRole:   "测试角色",
				GameArea:   "测试区域",
				GameServer: "测试服务器",
				BuyerIp:    "127.0.0.1",
				BuyerArea:  "测试地区",
			},
			Remark: "订单详情",
		},
	}, nil
}

// OrderCallback 订单回调
func (s *GoofishService) OrderCallback(ctx context.Context, req *goofishV1.OrderCallbackRequest) (*goofishV1.OrderCallbackResponse, error) {
	s.log.WithContext(ctx).Info("OrderCallback called")
	return &goofishV1.OrderCallbackResponse{
		Code: 0,
		Msg:  "回调处理成功",
	}, nil
}
