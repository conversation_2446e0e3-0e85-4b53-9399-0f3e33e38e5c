package data

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	conf "github.com/tx7do/kratos-bootstrap/api/gen/go/conf/v1"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData)

// Data .
type Data struct {
	log *log.Helper
}

// NewData .
func NewData(cfg *conf.Bootstrap, logger log.Logger) (*Data, func(), error) {
	l := log.NewHelper(log.With(logger, "module", "data/goofish-service"))

	d := &Data{
		log: l,
	}

	cleanup := func() {
		l.Info("closing the data resources")
	}

	return d, cleanup, nil
}
