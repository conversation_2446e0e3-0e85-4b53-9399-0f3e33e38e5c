# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: GoofishApi API
    description: 闲管家虚拟货源标准接口 - goofish API 路由配置，字段与 Apifox 文档保持一致
    version: 0.0.1
paths:
    /api/open/callback/virtual/goods/notify/{token}:
        post:
            tags:
                - GoofishApi
            description: 商品回调通知 - 注意：实际回调地址在订阅通知时传入
            operationId: GoofishApi_GoodsCallback
            parameters:
                - name: token
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsCallbackRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsCallbackResponse'
    /api/open/callback/virtual/order/notify/{token}:
        post:
            tags:
                - GoofishApi
            description: 订单回调通知 - 注意：实际回调地址在创建订单时传入
            operationId: GoofishApi_OrderCallback
            parameters:
                - name: token
                  in: path
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/OrderCallbackRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/OrderCallbackResponse'
    /goofish/goods/change/subscribe:
        post:
            tags:
                - GoofishApi
            description: 订阅商品变更通知 - 订阅货源商品价格、库存、状态变更通知
            operationId: GoofishApi_GoodsChangeSubscribe
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsChangeSubscribeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsChangeSubscribeResponse'
    /goofish/goods/change/subscribe/list:
        post:
            tags:
                - GoofishApi
            description: 查询商品订阅列表 - 查询已订阅商品变更通知的商品列表
            operationId: GoofishApi_GetGoodsChangeSubscribeList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsChangeSubscribeListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsChangeSubscribeListResponse'
    /goofish/goods/change/unsubscribe:
        post:
            tags:
                - GoofishApi
            description: 取消商品变更通知 - 取消订阅货源商品价格、库存、状态变更通知
            operationId: GoofishApi_GoodsChangeUnsubscribe
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsChangeUnsubscribeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsChangeUnsubscribeResponse'
    /goofish/goods/detail:
        post:
            tags:
                - GoofishApi
            description: 查询商品详情
            operationId: GoofishApi_GetGoodsDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsDetailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsDetailResponse'
    /goofish/goods/list:
        post:
            tags:
                - GoofishApi
            description: 查询商品列表
            operationId: GoofishApi_GetGoodsList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GoodsListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GoodsListResponse'
    /goofish/open/info:
        post:
            tags:
                - GoofishApi
            description: 查询平台信息
            operationId: GoofishApi_GetPlatformInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PlatformInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PlatformInfoResponse'
    /goofish/order/detail:
        post:
            tags:
                - GoofishApi
            description: 查询订单详情
            operationId: GoofishApi_GetOrderDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/OrderDetailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/OrderDetailResponse'
    /goofish/order/purchase/create:
        post:
            tags:
                - GoofishApi
            description: 创建卡密订单
            operationId: GoofishApi_CreateCardOrder
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateCardOrderRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateCardOrderResponse'
    /goofish/order/recharge/create:
        post:
            tags:
                - GoofishApi
            description: 创建直充订单
            operationId: GoofishApi_CreateRechargeOrder
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateRechargeOrderRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateRechargeOrderResponse'
    /goofish/user/info:
        post:
            tags:
                - GoofishApi
            description: 查询商户信息
            operationId: GoofishApi_GetUserInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UserInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserInfoResponse'
components:
    schemas:
        BizContent:
            type: object
            properties:
                account:
                    type: string
                game_name:
                    type: string
                game_role:
                    type: string
                game_area:
                    type: string
                game_server:
                    type: string
                buyer_ip:
                    type: string
                buyer_area:
                    type: string
            description: 订单相关
        CardItem:
            type: object
            properties:
                card_no:
                    type: string
                card_pwd:
                    type: string
        CardOrderData:
            type: object
            properties:
                order_no:
                    type: string
                out_order_no:
                    type: string
                order_status:
                    type: integer
                    format: int32
                order_amount:
                    type: string
                order_time:
                    type: string
                end_time:
                    type: string
                card_items:
                    type: array
                    items:
                        $ref: '#/components/schemas/CardItem'
                remark:
                    type: string
        CreateCardOrderRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
                order_no:
                    type: string
                    description: Body 参数
                goods_no:
                    type: string
                buy_quantity:
                    type: integer
                    format: int32
                max_amount:
                    type: string
                notify_url:
                    type: string
                biz_order_no:
                    type: string
            description: 创建卡密订单
        CreateCardOrderResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/CardOrderData'
        CreateRechargeOrderRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
                order_no:
                    type: string
                    description: Body 参数
                goods_no:
                    type: string
                biz_content:
                    $ref: '#/components/schemas/BizContent'
                buy_quantity:
                    type: integer
                    format: int32
                max_amount:
                    type: string
                notify_url:
                    type: string
                biz_order_no:
                    type: string
            description: 创建直充订单
        CreateRechargeOrderResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/RechargeOrderData'
        GoodsCallbackItem:
            type: object
            properties:
                goods_no:
                    type: string
                goods_type:
                    type: integer
                    format: int32
                price:
                    type: string
                stock:
                    type: integer
                    format: int32
                status:
                    type: integer
                    format: int32
                change_time:
                    type: string
            description: 商品回调通知
        GoodsCallbackRequest:
            type: object
            properties:
                token:
                    type: string
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoodsCallbackItem'
        GoodsCallbackResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        GoodsChangeSubscribeListData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoodsChangeSubscribeListItem'
                count:
                    type: integer
                    format: int32
        GoodsChangeSubscribeListItem:
            type: object
            properties:
                goods_type:
                    type: integer
                    format: int32
                goods_no:
                    type: string
                subscribe_time:
                    type: string
                token:
                    type: string
                notify_url:
                    type: string
        GoodsChangeSubscribeListRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
                goods_type:
                    type: integer
                    description: Body 参数
                    format: int32
                goods_no:
                    type: string
                page_no:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
            description: 查询商品订阅列表 - 完整请求
        GoodsChangeSubscribeListResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/GoodsChangeSubscribeListData'
        GoodsChangeSubscribeRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
                goods_type:
                    type: integer
                    description: Body 参数
                    format: int32
                goods_no:
                    type: string
                token:
                    type: string
                notify_url:
                    type: string
            description: 订阅商品变更通知
        GoodsChangeSubscribeResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        GoodsChangeUnsubscribeRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
                goods_type:
                    type: integer
                    description: Body 参数
                    format: int32
                goods_no:
                    type: string
                token:
                    type: string
            description: 取消商品变更通知
        GoodsChangeUnsubscribeResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        GoodsDetail:
            type: object
            properties:
                goods_no:
                    type: string
                goods_type:
                    type: integer
                    format: int32
                goods_name:
                    type: string
                price:
                    type: string
                stock:
                    type: integer
                    format: int32
                status:
                    type: integer
                    format: int32
                update_time:
                    type: string
                template:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoodsTemplate'
            description: 商品详情
        GoodsDetailRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
                goods_type:
                    type: integer
                    description: Body 参数
                    format: int32
                goods_no:
                    type: string
            description: 查询商品详情
        GoodsDetailResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/GoodsDetail'
        GoodsListData:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoodsDetail'
                count:
                    type: integer
                    format: int32
        GoodsListRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
                keyword:
                    type: string
                    description: Body 参数
                goods_type:
                    type: integer
                    format: int32
                page_no:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
            description: 查询商品列表
        GoodsListResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/GoodsListData'
        GoodsTemplate:
            type: object
            properties:
                code:
                    type: string
                name:
                    type: string
                desc:
                    type: string
                check:
                    type: integer
                    format: int32
            description: 商品详情模板
        OrderCallbackRequest:
            type: object
            properties:
                token:
                    type: string
                order_type:
                    type: integer
                    format: int32
                order_no:
                    type: string
                out_order_no:
                    type: string
                order_status:
                    type: integer
                    format: int32
                end_time:
                    type: string
                card_items:
                    type: array
                    items:
                        $ref: '#/components/schemas/CardItem'
                remark:
                    type: string
            description: 订单回调通知
        OrderCallbackResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        OrderDetailData:
            type: object
            properties:
                order_type:
                    type: integer
                    format: int32
                order_no:
                    type: string
                out_order_no:
                    type: string
                order_status:
                    type: integer
                    format: int32
                order_amount:
                    type: string
                goods_no:
                    type: string
                goods_name:
                    type: string
                buy_quantity:
                    type: integer
                    format: int32
                order_time:
                    type: string
                end_time:
                    type: string
                biz_content:
                    $ref: '#/components/schemas/BizContent'
                card_items:
                    type: array
                    items:
                        $ref: '#/components/schemas/CardItem'
                remark:
                    type: string
        OrderDetailRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
                order_type:
                    type: integer
                    description: Body 参数
                    format: int32
                order_no:
                    type: string
                out_order_no:
                    type: string
            description: 查询订单详情
        OrderDetailResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/OrderDetailData'
        PlatformInfoData:
            type: object
            properties:
                app_id:
                    type: string
        PlatformInfoRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
            description: 查询平台信息
        PlatformInfoResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/PlatformInfoData'
        RechargeOrderData:
            type: object
            properties:
                order_no:
                    type: string
                out_order_no:
                    type: string
                order_status:
                    type: integer
                    format: int32
                order_amount:
                    type: string
                goods_name:
                    type: string
                order_time:
                    type: string
                end_time:
                    type: string
                remark:
                    type: string
        UserInfoData:
            type: object
            properties:
                balance:
                    type: string
        UserInfoRequest:
            type: object
            properties:
                mch_id:
                    type: string
                    description: Query 参数
                timestamp:
                    type: string
                sign:
                    type: string
            description: 查询商户信息
        UserInfoResponse:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/UserInfoData'
tags:
    - name: GoofishApi
