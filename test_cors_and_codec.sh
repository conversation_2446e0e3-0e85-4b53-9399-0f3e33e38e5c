#!/bin/bash

echo "=== 测试 Goofish 服务的跨域和 CODEC 错误处理 ==="

# 服务器地址
SERVER_URL="http://localhost:8080"

echo ""
echo "1. 测试 CORS 预检请求 (OPTIONS)"
curl -X OPTIONS \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,Authorization" \
  -v \
  "$SERVER_URL/api/v1/goofish/test" 2>&1 | grep -E "(Access-Control|HTTP/)"

echo ""
echo "2. 测试正常的 GET 请求"
curl -X GET \
  -H "Origin: http://localhost:3000" \
  -H "Content-Type: application/json" \
  -v \
  "$SERVER_URL/api/v1/goofish/test" 2>&1 | grep -E "(Access-Control|HTTP/|Content-Type)"

echo ""
echo "3. 测试可能触发 CODEC 错误的请求 (无效的 Content-Type)"
curl -X POST \
  -H "Origin: http://localhost:3000" \
  -H "Content-Type: invalid/content-type" \
  -d '{"test": "data"}' \
  -v \
  "$SERVER_URL/api/v1/goofish/test" 2>&1 | grep -E "(Access-Control|HTTP/|Content-Type|CODEC)"

echo ""
echo "4. 测试空的 Content-Type"
curl -X POST \
  -H "Origin: http://localhost:3000" \
  -H "Content-Type: " \
  -d '{"test": "data"}' \
  -v \
  "$SERVER_URL/api/v1/goofish/test" 2>&1 | grep -E "(Access-Control|HTTP/|Content-Type|CODEC)"

echo ""
echo "5. 测试健康检查端点"
curl -X GET \
  -H "Origin: http://localhost:3000" \
  -v \
  "$SERVER_URL/health" 2>&1 | grep -E "(Access-Control|HTTP/)"

echo ""
echo "=== 测试完成 ==="
