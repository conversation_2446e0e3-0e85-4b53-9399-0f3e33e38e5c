// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: goofish/service/v1/goofish.proto

package goofishv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 查询平台信息
type PlatformInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId         string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp     int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign          string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlatformInfoRequest) Reset() {
	*x = PlatformInfoRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlatformInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformInfoRequest) ProtoMessage() {}

func (x *PlatformInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformInfoRequest.ProtoReflect.Descriptor instead.
func (*PlatformInfoRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{0}
}

func (x *PlatformInfoRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *PlatformInfoRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *PlatformInfoRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

type PlatformInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *PlatformInfoData      `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlatformInfoResponse) Reset() {
	*x = PlatformInfoResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlatformInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformInfoResponse) ProtoMessage() {}

func (x *PlatformInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformInfoResponse.ProtoReflect.Descriptor instead.
func (*PlatformInfoResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{1}
}

func (x *PlatformInfoResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PlatformInfoResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PlatformInfoResponse) GetData() *PlatformInfoData {
	if x != nil {
		return x.Data
	}
	return nil
}

type PlatformInfoData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppId         int64                  `protobuf:"varint,1,opt,name=app_id,proto3" json:"app_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlatformInfoData) Reset() {
	*x = PlatformInfoData{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlatformInfoData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformInfoData) ProtoMessage() {}

func (x *PlatformInfoData) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformInfoData.ProtoReflect.Descriptor instead.
func (*PlatformInfoData) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{2}
}

func (x *PlatformInfoData) GetAppId() int64 {
	if x != nil {
		return x.AppId
	}
	return 0
}

// 查询商户信息
type UserInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId         string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp     int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign          string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfoRequest) Reset() {
	*x = UserInfoRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoRequest) ProtoMessage() {}

func (x *UserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoRequest.ProtoReflect.Descriptor instead.
func (*UserInfoRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{3}
}

func (x *UserInfoRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *UserInfoRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *UserInfoRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

type UserInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *UserInfoData          `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfoResponse) Reset() {
	*x = UserInfoResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoResponse) ProtoMessage() {}

func (x *UserInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoResponse.ProtoReflect.Descriptor instead.
func (*UserInfoResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{4}
}

func (x *UserInfoResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserInfoResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UserInfoResponse) GetData() *UserInfoData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserInfoData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Balance       int64                  `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfoData) Reset() {
	*x = UserInfoData{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoData) ProtoMessage() {}

func (x *UserInfoData) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoData.ProtoReflect.Descriptor instead.
func (*UserInfoData) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{5}
}

func (x *UserInfoData) GetBalance() int64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

// 商品详情模板
type GoodsTemplate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc          string                 `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Check         int32                  `protobuf:"varint,4,opt,name=check,proto3" json:"check,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsTemplate) Reset() {
	*x = GoodsTemplate{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsTemplate) ProtoMessage() {}

func (x *GoodsTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsTemplate.ProtoReflect.Descriptor instead.
func (*GoodsTemplate) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{6}
}

func (x *GoodsTemplate) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GoodsTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsTemplate) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *GoodsTemplate) GetCheck() int32 {
	if x != nil {
		return x.Check
	}
	return 0
}

// 商品详情
type GoodsDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GoodsNo       string                 `protobuf:"bytes,1,opt,name=goods_no,proto3" json:"goods_no,omitempty"`
	GoodsType     int32                  `protobuf:"varint,2,opt,name=goods_type,proto3" json:"goods_type,omitempty"`
	GoodsName     string                 `protobuf:"bytes,3,opt,name=goods_name,proto3" json:"goods_name,omitempty"`
	Price         int64                  `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Stock         int32                  `protobuf:"varint,5,opt,name=stock,proto3" json:"stock,omitempty"`
	Status        int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	UpdateTime    int64                  `protobuf:"varint,7,opt,name=update_time,proto3" json:"update_time,omitempty"`
	Template      []*GoodsTemplate       `protobuf:"bytes,8,rep,name=template,proto3" json:"template,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsDetail) Reset() {
	*x = GoodsDetail{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsDetail) ProtoMessage() {}

func (x *GoodsDetail) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsDetail.ProtoReflect.Descriptor instead.
func (*GoodsDetail) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{7}
}

func (x *GoodsDetail) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *GoodsDetail) GetGoodsType() int32 {
	if x != nil {
		return x.GoodsType
	}
	return 0
}

func (x *GoodsDetail) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *GoodsDetail) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GoodsDetail) GetStock() int32 {
	if x != nil {
		return x.Stock
	}
	return 0
}

func (x *GoodsDetail) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GoodsDetail) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *GoodsDetail) GetTemplate() []*GoodsTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

// 查询商品详情
type GoodsDetailRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId     string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	// Body 参数
	GoodsType     int32  `protobuf:"varint,4,opt,name=goods_type,proto3" json:"goods_type,omitempty"` // 商品类型：1=直充商品，2=卡密商品
	GoodsNo       string `protobuf:"bytes,5,opt,name=goods_no,proto3" json:"goods_no,omitempty"`      // 商品编码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsDetailRequest) Reset() {
	*x = GoodsDetailRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsDetailRequest) ProtoMessage() {}

func (x *GoodsDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsDetailRequest.ProtoReflect.Descriptor instead.
func (*GoodsDetailRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{8}
}

func (x *GoodsDetailRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *GoodsDetailRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GoodsDetailRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *GoodsDetailRequest) GetGoodsType() int32 {
	if x != nil {
		return x.GoodsType
	}
	return 0
}

func (x *GoodsDetailRequest) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

type GoodsDetailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GoodsDetail           `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsDetailResponse) Reset() {
	*x = GoodsDetailResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsDetailResponse) ProtoMessage() {}

func (x *GoodsDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsDetailResponse.ProtoReflect.Descriptor instead.
func (*GoodsDetailResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{9}
}

func (x *GoodsDetailResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GoodsDetailResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GoodsDetailResponse) GetData() *GoodsDetail {
	if x != nil {
		return x.Data
	}
	return nil
}

// 查询商品列表
type GoodsListRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId     string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	// Body 参数
	Keyword       string `protobuf:"bytes,4,opt,name=keyword,proto3" json:"keyword,omitempty"`        // 搜索关键词
	GoodsType     int32  `protobuf:"varint,5,opt,name=goods_type,proto3" json:"goods_type,omitempty"` // 商品类型：1=直充商品，2=卡密商品
	PageNo        int32  `protobuf:"varint,6,opt,name=page_no,proto3" json:"page_no,omitempty"`       // 当前页码
	PageSize      int32  `protobuf:"varint,7,opt,name=page_size,proto3" json:"page_size,omitempty"`   // 每页行数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsListRequest) Reset() {
	*x = GoodsListRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsListRequest) ProtoMessage() {}

func (x *GoodsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsListRequest.ProtoReflect.Descriptor instead.
func (*GoodsListRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{10}
}

func (x *GoodsListRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *GoodsListRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GoodsListRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *GoodsListRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *GoodsListRequest) GetGoodsType() int32 {
	if x != nil {
		return x.GoodsType
	}
	return 0
}

func (x *GoodsListRequest) GetPageNo() int32 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GoodsListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GoodsListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GoodsListData         `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsListResponse) Reset() {
	*x = GoodsListResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsListResponse) ProtoMessage() {}

func (x *GoodsListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsListResponse.ProtoReflect.Descriptor instead.
func (*GoodsListResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{11}
}

func (x *GoodsListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GoodsListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GoodsListResponse) GetData() *GoodsListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GoodsListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*GoodsDetail         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsListData) Reset() {
	*x = GoodsListData{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsListData) ProtoMessage() {}

func (x *GoodsListData) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsListData.ProtoReflect.Descriptor instead.
func (*GoodsListData) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{12}
}

func (x *GoodsListData) GetList() []*GoodsDetail {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GoodsListData) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 订阅商品变更通知
type GoodsChangeSubscribeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId     string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	// Body 参数
	GoodsType     int32  `protobuf:"varint,4,opt,name=goods_type,proto3" json:"goods_type,omitempty"` // 商品类型：1=直充商品，2=卡密商品
	GoodsNo       string `protobuf:"bytes,5,opt,name=goods_no,proto3" json:"goods_no,omitempty"`      // 商品编码
	Token         string `protobuf:"bytes,6,opt,name=token,proto3" json:"token,omitempty"`            // 订阅标识
	NotifyUrl     string `protobuf:"bytes,7,opt,name=notify_url,proto3" json:"notify_url,omitempty"`  // 回调地址
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsChangeSubscribeRequest) Reset() {
	*x = GoodsChangeSubscribeRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsChangeSubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsChangeSubscribeRequest) ProtoMessage() {}

func (x *GoodsChangeSubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsChangeSubscribeRequest.ProtoReflect.Descriptor instead.
func (*GoodsChangeSubscribeRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{13}
}

func (x *GoodsChangeSubscribeRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *GoodsChangeSubscribeRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GoodsChangeSubscribeRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *GoodsChangeSubscribeRequest) GetGoodsType() int32 {
	if x != nil {
		return x.GoodsType
	}
	return 0
}

func (x *GoodsChangeSubscribeRequest) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *GoodsChangeSubscribeRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GoodsChangeSubscribeRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

type GoodsChangeSubscribeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsChangeSubscribeResponse) Reset() {
	*x = GoodsChangeSubscribeResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsChangeSubscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsChangeSubscribeResponse) ProtoMessage() {}

func (x *GoodsChangeSubscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsChangeSubscribeResponse.ProtoReflect.Descriptor instead.
func (*GoodsChangeSubscribeResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{14}
}

func (x *GoodsChangeSubscribeResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GoodsChangeSubscribeResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 取消商品变更通知
type GoodsChangeUnsubscribeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId     string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	// Body 参数
	GoodsType     int32  `protobuf:"varint,4,opt,name=goods_type,proto3" json:"goods_type,omitempty"` // 商品类型：1=直充商品，2=卡密商品
	GoodsNo       string `protobuf:"bytes,5,opt,name=goods_no,proto3" json:"goods_no,omitempty"`      // 商品编码
	Token         string `protobuf:"bytes,6,opt,name=token,proto3" json:"token,omitempty"`            // 订阅标识
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsChangeUnsubscribeRequest) Reset() {
	*x = GoodsChangeUnsubscribeRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsChangeUnsubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsChangeUnsubscribeRequest) ProtoMessage() {}

func (x *GoodsChangeUnsubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsChangeUnsubscribeRequest.ProtoReflect.Descriptor instead.
func (*GoodsChangeUnsubscribeRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{15}
}

func (x *GoodsChangeUnsubscribeRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *GoodsChangeUnsubscribeRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GoodsChangeUnsubscribeRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *GoodsChangeUnsubscribeRequest) GetGoodsType() int32 {
	if x != nil {
		return x.GoodsType
	}
	return 0
}

func (x *GoodsChangeUnsubscribeRequest) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *GoodsChangeUnsubscribeRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type GoodsChangeUnsubscribeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsChangeUnsubscribeResponse) Reset() {
	*x = GoodsChangeUnsubscribeResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsChangeUnsubscribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsChangeUnsubscribeResponse) ProtoMessage() {}

func (x *GoodsChangeUnsubscribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsChangeUnsubscribeResponse.ProtoReflect.Descriptor instead.
func (*GoodsChangeUnsubscribeResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{16}
}

func (x *GoodsChangeUnsubscribeResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GoodsChangeUnsubscribeResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 查询商品订阅列表 - Body 参数
type GoodsChangeSubscribeListBody struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GoodsType     int32                  `protobuf:"varint,1,opt,name=goods_type,proto3" json:"goods_type,omitempty"` // 商品类型：1=直充商品，2=卡密商品
	GoodsNo       string                 `protobuf:"bytes,2,opt,name=goods_no,proto3" json:"goods_no,omitempty"`      // 商品编码
	PageNo        int32                  `protobuf:"varint,3,opt,name=page_no,proto3" json:"page_no,omitempty"`       // 当前页码
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,proto3" json:"page_size,omitempty"`   // 每页行数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsChangeSubscribeListBody) Reset() {
	*x = GoodsChangeSubscribeListBody{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsChangeSubscribeListBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsChangeSubscribeListBody) ProtoMessage() {}

func (x *GoodsChangeSubscribeListBody) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsChangeSubscribeListBody.ProtoReflect.Descriptor instead.
func (*GoodsChangeSubscribeListBody) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{17}
}

func (x *GoodsChangeSubscribeListBody) GetGoodsType() int32 {
	if x != nil {
		return x.GoodsType
	}
	return 0
}

func (x *GoodsChangeSubscribeListBody) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *GoodsChangeSubscribeListBody) GetPageNo() int32 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GoodsChangeSubscribeListBody) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 查询商品订阅列表 - 完整请求
type GoodsChangeSubscribeListRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId     string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	// Body 参数
	GoodsType     int32  `protobuf:"varint,4,opt,name=goods_type,proto3" json:"goods_type,omitempty"` // 商品类型：1=直充商品，2=卡密商品
	GoodsNo       string `protobuf:"bytes,5,opt,name=goods_no,proto3" json:"goods_no,omitempty"`      // 商品编码
	PageNo        int32  `protobuf:"varint,6,opt,name=page_no,proto3" json:"page_no,omitempty"`       // 当前页码
	PageSize      int32  `protobuf:"varint,7,opt,name=page_size,proto3" json:"page_size,omitempty"`   // 每页行数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsChangeSubscribeListRequest) Reset() {
	*x = GoodsChangeSubscribeListRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsChangeSubscribeListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsChangeSubscribeListRequest) ProtoMessage() {}

func (x *GoodsChangeSubscribeListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsChangeSubscribeListRequest.ProtoReflect.Descriptor instead.
func (*GoodsChangeSubscribeListRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{18}
}

func (x *GoodsChangeSubscribeListRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *GoodsChangeSubscribeListRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GoodsChangeSubscribeListRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *GoodsChangeSubscribeListRequest) GetGoodsType() int32 {
	if x != nil {
		return x.GoodsType
	}
	return 0
}

func (x *GoodsChangeSubscribeListRequest) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *GoodsChangeSubscribeListRequest) GetPageNo() int32 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GoodsChangeSubscribeListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GoodsChangeSubscribeListResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Code          int32                         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GoodsChangeSubscribeListData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsChangeSubscribeListResponse) Reset() {
	*x = GoodsChangeSubscribeListResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsChangeSubscribeListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsChangeSubscribeListResponse) ProtoMessage() {}

func (x *GoodsChangeSubscribeListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsChangeSubscribeListResponse.ProtoReflect.Descriptor instead.
func (*GoodsChangeSubscribeListResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{19}
}

func (x *GoodsChangeSubscribeListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GoodsChangeSubscribeListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GoodsChangeSubscribeListResponse) GetData() *GoodsChangeSubscribeListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GoodsChangeSubscribeListData struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	List          []*GoodsChangeSubscribeListItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count         int32                           `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsChangeSubscribeListData) Reset() {
	*x = GoodsChangeSubscribeListData{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsChangeSubscribeListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsChangeSubscribeListData) ProtoMessage() {}

func (x *GoodsChangeSubscribeListData) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsChangeSubscribeListData.ProtoReflect.Descriptor instead.
func (*GoodsChangeSubscribeListData) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{20}
}

func (x *GoodsChangeSubscribeListData) GetList() []*GoodsChangeSubscribeListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GoodsChangeSubscribeListData) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GoodsChangeSubscribeListItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GoodsType     int32                  `protobuf:"varint,1,opt,name=goods_type,proto3" json:"goods_type,omitempty"`
	GoodsNo       string                 `protobuf:"bytes,2,opt,name=goods_no,proto3" json:"goods_no,omitempty"`
	SubscribeTime int64                  `protobuf:"varint,3,opt,name=subscribe_time,proto3" json:"subscribe_time,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	NotifyUrl     string                 `protobuf:"bytes,5,opt,name=notify_url,proto3" json:"notify_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsChangeSubscribeListItem) Reset() {
	*x = GoodsChangeSubscribeListItem{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsChangeSubscribeListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsChangeSubscribeListItem) ProtoMessage() {}

func (x *GoodsChangeSubscribeListItem) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsChangeSubscribeListItem.ProtoReflect.Descriptor instead.
func (*GoodsChangeSubscribeListItem) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{21}
}

func (x *GoodsChangeSubscribeListItem) GetGoodsType() int32 {
	if x != nil {
		return x.GoodsType
	}
	return 0
}

func (x *GoodsChangeSubscribeListItem) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *GoodsChangeSubscribeListItem) GetSubscribeTime() int64 {
	if x != nil {
		return x.SubscribeTime
	}
	return 0
}

func (x *GoodsChangeSubscribeListItem) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GoodsChangeSubscribeListItem) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

// 商品回调通知
type GoodsCallbackItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GoodsNo       string                 `protobuf:"bytes,1,opt,name=goods_no,proto3" json:"goods_no,omitempty"`
	GoodsType     int32                  `protobuf:"varint,2,opt,name=goods_type,proto3" json:"goods_type,omitempty"`
	Price         int64                  `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	Stock         int32                  `protobuf:"varint,4,opt,name=stock,proto3" json:"stock,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	ChangeTime    int64                  `protobuf:"varint,6,opt,name=change_time,proto3" json:"change_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsCallbackItem) Reset() {
	*x = GoodsCallbackItem{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsCallbackItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsCallbackItem) ProtoMessage() {}

func (x *GoodsCallbackItem) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsCallbackItem.ProtoReflect.Descriptor instead.
func (*GoodsCallbackItem) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{22}
}

func (x *GoodsCallbackItem) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *GoodsCallbackItem) GetGoodsType() int32 {
	if x != nil {
		return x.GoodsType
	}
	return 0
}

func (x *GoodsCallbackItem) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GoodsCallbackItem) GetStock() int32 {
	if x != nil {
		return x.Stock
	}
	return 0
}

func (x *GoodsCallbackItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GoodsCallbackItem) GetChangeTime() int64 {
	if x != nil {
		return x.ChangeTime
	}
	return 0
}

type GoodsCallbackRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"` // 回调令牌，用于验证回调请求的合法性
	Items         []*GoodsCallbackItem   `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsCallbackRequest) Reset() {
	*x = GoodsCallbackRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsCallbackRequest) ProtoMessage() {}

func (x *GoodsCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsCallbackRequest.ProtoReflect.Descriptor instead.
func (*GoodsCallbackRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{23}
}

func (x *GoodsCallbackRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GoodsCallbackRequest) GetItems() []*GoodsCallbackItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type GoodsCallbackResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsCallbackResponse) Reset() {
	*x = GoodsCallbackResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsCallbackResponse) ProtoMessage() {}

func (x *GoodsCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsCallbackResponse.ProtoReflect.Descriptor instead.
func (*GoodsCallbackResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{24}
}

func (x *GoodsCallbackResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GoodsCallbackResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 订单相关
type BizContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	GameName      string                 `protobuf:"bytes,2,opt,name=game_name,proto3" json:"game_name,omitempty"`
	GameRole      string                 `protobuf:"bytes,3,opt,name=game_role,proto3" json:"game_role,omitempty"`
	GameArea      string                 `protobuf:"bytes,4,opt,name=game_area,proto3" json:"game_area,omitempty"`
	GameServer    string                 `protobuf:"bytes,5,opt,name=game_server,proto3" json:"game_server,omitempty"`
	BuyerIp       string                 `protobuf:"bytes,6,opt,name=buyer_ip,proto3" json:"buyer_ip,omitempty"`
	BuyerArea     string                 `protobuf:"bytes,7,opt,name=buyer_area,proto3" json:"buyer_area,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BizContent) Reset() {
	*x = BizContent{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BizContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BizContent) ProtoMessage() {}

func (x *BizContent) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BizContent.ProtoReflect.Descriptor instead.
func (*BizContent) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{25}
}

func (x *BizContent) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *BizContent) GetGameName() string {
	if x != nil {
		return x.GameName
	}
	return ""
}

func (x *BizContent) GetGameRole() string {
	if x != nil {
		return x.GameRole
	}
	return ""
}

func (x *BizContent) GetGameArea() string {
	if x != nil {
		return x.GameArea
	}
	return ""
}

func (x *BizContent) GetGameServer() string {
	if x != nil {
		return x.GameServer
	}
	return ""
}

func (x *BizContent) GetBuyerIp() string {
	if x != nil {
		return x.BuyerIp
	}
	return ""
}

func (x *BizContent) GetBuyerArea() string {
	if x != nil {
		return x.BuyerArea
	}
	return ""
}

type CardItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CardNo        string                 `protobuf:"bytes,1,opt,name=card_no,proto3" json:"card_no,omitempty"`
	CardPwd       string                 `protobuf:"bytes,2,opt,name=card_pwd,proto3" json:"card_pwd,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardItem) Reset() {
	*x = CardItem{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardItem) ProtoMessage() {}

func (x *CardItem) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardItem.ProtoReflect.Descriptor instead.
func (*CardItem) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{26}
}

func (x *CardItem) GetCardNo() string {
	if x != nil {
		return x.CardNo
	}
	return ""
}

func (x *CardItem) GetCardPwd() string {
	if x != nil {
		return x.CardPwd
	}
	return ""
}

// 创建直充订单
type CreateRechargeOrderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId     string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	// Body 参数
	OrderNo       string      `protobuf:"bytes,4,opt,name=order_no,proto3" json:"order_no,omitempty"`          // 订单号
	GoodsNo       string      `protobuf:"bytes,5,opt,name=goods_no,proto3" json:"goods_no,omitempty"`          // 商品编码
	BizContent    *BizContent `protobuf:"bytes,6,opt,name=biz_content,proto3" json:"biz_content,omitempty"`    // 业务内容
	BuyQuantity   int32       `protobuf:"varint,7,opt,name=buy_quantity,proto3" json:"buy_quantity,omitempty"` // 购买数量
	MaxAmount     int64       `protobuf:"varint,8,opt,name=max_amount,proto3" json:"max_amount,omitempty"`     // 最大金额
	NotifyUrl     string      `protobuf:"bytes,9,opt,name=notify_url,proto3" json:"notify_url,omitempty"`      // 回调地址
	BizOrderNo    string      `protobuf:"bytes,10,opt,name=biz_order_no,proto3" json:"biz_order_no,omitempty"` // 业务订单号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRechargeOrderRequest) Reset() {
	*x = CreateRechargeOrderRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRechargeOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRechargeOrderRequest) ProtoMessage() {}

func (x *CreateRechargeOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRechargeOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateRechargeOrderRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{27}
}

func (x *CreateRechargeOrderRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *CreateRechargeOrderRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetBizContent() *BizContent {
	if x != nil {
		return x.BizContent
	}
	return nil
}

func (x *CreateRechargeOrderRequest) GetBuyQuantity() int32 {
	if x != nil {
		return x.BuyQuantity
	}
	return 0
}

func (x *CreateRechargeOrderRequest) GetMaxAmount() int64 {
	if x != nil {
		return x.MaxAmount
	}
	return 0
}

func (x *CreateRechargeOrderRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetBizOrderNo() string {
	if x != nil {
		return x.BizOrderNo
	}
	return ""
}

type CreateRechargeOrderResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *RechargeOrderData     `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRechargeOrderResponse) Reset() {
	*x = CreateRechargeOrderResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRechargeOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRechargeOrderResponse) ProtoMessage() {}

func (x *CreateRechargeOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRechargeOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateRechargeOrderResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{28}
}

func (x *CreateRechargeOrderResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateRechargeOrderResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateRechargeOrderResponse) GetData() *RechargeOrderData {
	if x != nil {
		return x.Data
	}
	return nil
}

type RechargeOrderData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderNo       string                 `protobuf:"bytes,1,opt,name=order_no,proto3" json:"order_no,omitempty"`
	OutOrderNo    string                 `protobuf:"bytes,2,opt,name=out_order_no,proto3" json:"out_order_no,omitempty"`
	OrderStatus   int32                  `protobuf:"varint,3,opt,name=order_status,proto3" json:"order_status,omitempty"`
	OrderAmount   int64                  `protobuf:"varint,4,opt,name=order_amount,proto3" json:"order_amount,omitempty"`
	GoodsName     string                 `protobuf:"bytes,5,opt,name=goods_name,proto3" json:"goods_name,omitempty"`
	OrderTime     int64                  `protobuf:"varint,6,opt,name=order_time,proto3" json:"order_time,omitempty"`
	EndTime       int64                  `protobuf:"varint,7,opt,name=end_time,proto3" json:"end_time,omitempty"`
	Remark        string                 `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RechargeOrderData) Reset() {
	*x = RechargeOrderData{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RechargeOrderData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeOrderData) ProtoMessage() {}

func (x *RechargeOrderData) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeOrderData.ProtoReflect.Descriptor instead.
func (*RechargeOrderData) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{29}
}

func (x *RechargeOrderData) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *RechargeOrderData) GetOutOrderNo() string {
	if x != nil {
		return x.OutOrderNo
	}
	return ""
}

func (x *RechargeOrderData) GetOrderStatus() int32 {
	if x != nil {
		return x.OrderStatus
	}
	return 0
}

func (x *RechargeOrderData) GetOrderAmount() int64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *RechargeOrderData) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *RechargeOrderData) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *RechargeOrderData) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *RechargeOrderData) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// 创建卡密订单
type CreateCardOrderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId     string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	// Body 参数
	OrderNo       string `protobuf:"bytes,4,opt,name=order_no,proto3" json:"order_no,omitempty"`          // 订单号
	GoodsNo       string `protobuf:"bytes,5,opt,name=goods_no,proto3" json:"goods_no,omitempty"`          // 商品编码
	BuyQuantity   int32  `protobuf:"varint,6,opt,name=buy_quantity,proto3" json:"buy_quantity,omitempty"` // 购买数量
	MaxAmount     int64  `protobuf:"varint,7,opt,name=max_amount,proto3" json:"max_amount,omitempty"`     // 最大金额
	NotifyUrl     string `protobuf:"bytes,8,opt,name=notify_url,proto3" json:"notify_url,omitempty"`      // 回调地址
	BizOrderNo    string `protobuf:"bytes,9,opt,name=biz_order_no,proto3" json:"biz_order_no,omitempty"`  // 业务订单号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCardOrderRequest) Reset() {
	*x = CreateCardOrderRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCardOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCardOrderRequest) ProtoMessage() {}

func (x *CreateCardOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCardOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateCardOrderRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{30}
}

func (x *CreateCardOrderRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *CreateCardOrderRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *CreateCardOrderRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *CreateCardOrderRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *CreateCardOrderRequest) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *CreateCardOrderRequest) GetBuyQuantity() int32 {
	if x != nil {
		return x.BuyQuantity
	}
	return 0
}

func (x *CreateCardOrderRequest) GetMaxAmount() int64 {
	if x != nil {
		return x.MaxAmount
	}
	return 0
}

func (x *CreateCardOrderRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *CreateCardOrderRequest) GetBizOrderNo() string {
	if x != nil {
		return x.BizOrderNo
	}
	return ""
}

type CreateCardOrderResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *CardOrderData         `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCardOrderResponse) Reset() {
	*x = CreateCardOrderResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCardOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCardOrderResponse) ProtoMessage() {}

func (x *CreateCardOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCardOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateCardOrderResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{31}
}

func (x *CreateCardOrderResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateCardOrderResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateCardOrderResponse) GetData() *CardOrderData {
	if x != nil {
		return x.Data
	}
	return nil
}

type CardOrderData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderNo       string                 `protobuf:"bytes,1,opt,name=order_no,proto3" json:"order_no,omitempty"`
	OutOrderNo    string                 `protobuf:"bytes,2,opt,name=out_order_no,proto3" json:"out_order_no,omitempty"`
	OrderStatus   int32                  `protobuf:"varint,3,opt,name=order_status,proto3" json:"order_status,omitempty"`
	OrderAmount   int64                  `protobuf:"varint,4,opt,name=order_amount,proto3" json:"order_amount,omitempty"`
	OrderTime     int64                  `protobuf:"varint,5,opt,name=order_time,proto3" json:"order_time,omitempty"`
	EndTime       int64                  `protobuf:"varint,6,opt,name=end_time,proto3" json:"end_time,omitempty"`
	CardItems     []*CardItem            `protobuf:"bytes,7,rep,name=card_items,proto3" json:"card_items,omitempty"`
	Remark        string                 `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardOrderData) Reset() {
	*x = CardOrderData{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardOrderData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardOrderData) ProtoMessage() {}

func (x *CardOrderData) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardOrderData.ProtoReflect.Descriptor instead.
func (*CardOrderData) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{32}
}

func (x *CardOrderData) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *CardOrderData) GetOutOrderNo() string {
	if x != nil {
		return x.OutOrderNo
	}
	return ""
}

func (x *CardOrderData) GetOrderStatus() int32 {
	if x != nil {
		return x.OrderStatus
	}
	return 0
}

func (x *CardOrderData) GetOrderAmount() int64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *CardOrderData) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *CardOrderData) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *CardOrderData) GetCardItems() []*CardItem {
	if x != nil {
		return x.CardItems
	}
	return nil
}

func (x *CardOrderData) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// 查询订单详情
type OrderDetailRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Query 参数
	MchId     string `protobuf:"bytes,1,opt,name=mch_id,proto3" json:"mch_id,omitempty"`        // 货源平台商户ID（AppKey）
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 当前时间戳（单位秒，5分钟内有效）
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`            // 签名MD5值（参考签名说明）
	// Body 参数
	OrderType     int32  `protobuf:"varint,4,opt,name=order_type,proto3" json:"order_type,omitempty"`    // 订单类型
	OrderNo       string `protobuf:"bytes,5,opt,name=order_no,proto3" json:"order_no,omitempty"`         // 订单号
	OutOrderNo    string `protobuf:"bytes,6,opt,name=out_order_no,proto3" json:"out_order_no,omitempty"` // 外部订单号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderDetailRequest) Reset() {
	*x = OrderDetailRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailRequest) ProtoMessage() {}

func (x *OrderDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailRequest.ProtoReflect.Descriptor instead.
func (*OrderDetailRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{33}
}

func (x *OrderDetailRequest) GetMchId() string {
	if x != nil {
		return x.MchId
	}
	return ""
}

func (x *OrderDetailRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *OrderDetailRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *OrderDetailRequest) GetOrderType() int32 {
	if x != nil {
		return x.OrderType
	}
	return 0
}

func (x *OrderDetailRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *OrderDetailRequest) GetOutOrderNo() string {
	if x != nil {
		return x.OutOrderNo
	}
	return ""
}

type OrderDetailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *OrderDetailData       `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderDetailResponse) Reset() {
	*x = OrderDetailResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailResponse) ProtoMessage() {}

func (x *OrderDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailResponse.ProtoReflect.Descriptor instead.
func (*OrderDetailResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{34}
}

func (x *OrderDetailResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *OrderDetailResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *OrderDetailResponse) GetData() *OrderDetailData {
	if x != nil {
		return x.Data
	}
	return nil
}

type OrderDetailData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderType     int32                  `protobuf:"varint,1,opt,name=order_type,proto3" json:"order_type,omitempty"`
	OrderNo       string                 `protobuf:"bytes,2,opt,name=order_no,proto3" json:"order_no,omitempty"`
	OutOrderNo    string                 `protobuf:"bytes,3,opt,name=out_order_no,proto3" json:"out_order_no,omitempty"`
	OrderStatus   int32                  `protobuf:"varint,4,opt,name=order_status,proto3" json:"order_status,omitempty"`
	OrderAmount   int64                  `protobuf:"varint,5,opt,name=order_amount,proto3" json:"order_amount,omitempty"`
	GoodsNo       string                 `protobuf:"bytes,6,opt,name=goods_no,proto3" json:"goods_no,omitempty"`
	GoodsName     string                 `protobuf:"bytes,7,opt,name=goods_name,proto3" json:"goods_name,omitempty"`
	BuyQuantity   int32                  `protobuf:"varint,8,opt,name=buy_quantity,proto3" json:"buy_quantity,omitempty"`
	OrderTime     int64                  `protobuf:"varint,9,opt,name=order_time,proto3" json:"order_time,omitempty"`
	EndTime       int64                  `protobuf:"varint,10,opt,name=end_time,proto3" json:"end_time,omitempty"`
	BizContent    *BizContent            `protobuf:"bytes,11,opt,name=biz_content,proto3" json:"biz_content,omitempty"`
	CardItems     []*CardItem            `protobuf:"bytes,12,rep,name=card_items,proto3" json:"card_items,omitempty"`
	Remark        string                 `protobuf:"bytes,13,opt,name=remark,proto3" json:"remark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderDetailData) Reset() {
	*x = OrderDetailData{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderDetailData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailData) ProtoMessage() {}

func (x *OrderDetailData) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailData.ProtoReflect.Descriptor instead.
func (*OrderDetailData) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{35}
}

func (x *OrderDetailData) GetOrderType() int32 {
	if x != nil {
		return x.OrderType
	}
	return 0
}

func (x *OrderDetailData) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *OrderDetailData) GetOutOrderNo() string {
	if x != nil {
		return x.OutOrderNo
	}
	return ""
}

func (x *OrderDetailData) GetOrderStatus() int32 {
	if x != nil {
		return x.OrderStatus
	}
	return 0
}

func (x *OrderDetailData) GetOrderAmount() int64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *OrderDetailData) GetGoodsNo() string {
	if x != nil {
		return x.GoodsNo
	}
	return ""
}

func (x *OrderDetailData) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *OrderDetailData) GetBuyQuantity() int32 {
	if x != nil {
		return x.BuyQuantity
	}
	return 0
}

func (x *OrderDetailData) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *OrderDetailData) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *OrderDetailData) GetBizContent() *BizContent {
	if x != nil {
		return x.BizContent
	}
	return nil
}

func (x *OrderDetailData) GetCardItems() []*CardItem {
	if x != nil {
		return x.CardItems
	}
	return nil
}

func (x *OrderDetailData) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

// 订单回调通知
type OrderCallbackRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"` // 回调令牌，用于验证回调请求的合法性
	OrderType     int32                  `protobuf:"varint,2,opt,name=order_type,proto3" json:"order_type,omitempty"`
	OrderNo       string                 `protobuf:"bytes,3,opt,name=order_no,proto3" json:"order_no,omitempty"`
	OutOrderNo    string                 `protobuf:"bytes,4,opt,name=out_order_no,proto3" json:"out_order_no,omitempty"`
	OrderStatus   int32                  `protobuf:"varint,5,opt,name=order_status,proto3" json:"order_status,omitempty"`
	EndTime       int64                  `protobuf:"varint,6,opt,name=end_time,proto3" json:"end_time,omitempty"`
	CardItems     []*CardItem            `protobuf:"bytes,7,rep,name=card_items,proto3" json:"card_items,omitempty"`
	Remark        string                 `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderCallbackRequest) Reset() {
	*x = OrderCallbackRequest{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderCallbackRequest) ProtoMessage() {}

func (x *OrderCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderCallbackRequest.ProtoReflect.Descriptor instead.
func (*OrderCallbackRequest) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{36}
}

func (x *OrderCallbackRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *OrderCallbackRequest) GetOrderType() int32 {
	if x != nil {
		return x.OrderType
	}
	return 0
}

func (x *OrderCallbackRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *OrderCallbackRequest) GetOutOrderNo() string {
	if x != nil {
		return x.OutOrderNo
	}
	return ""
}

func (x *OrderCallbackRequest) GetOrderStatus() int32 {
	if x != nil {
		return x.OrderStatus
	}
	return 0
}

func (x *OrderCallbackRequest) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *OrderCallbackRequest) GetCardItems() []*CardItem {
	if x != nil {
		return x.CardItems
	}
	return nil
}

func (x *OrderCallbackRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type OrderCallbackResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderCallbackResponse) Reset() {
	*x = OrderCallbackResponse{}
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderCallbackResponse) ProtoMessage() {}

func (x *OrderCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_goofish_service_v1_goofish_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderCallbackResponse.ProtoReflect.Descriptor instead.
func (*OrderCallbackResponse) Descriptor() ([]byte, []int) {
	return file_goofish_service_v1_goofish_proto_rawDescGZIP(), []int{37}
}

func (x *OrderCallbackResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *OrderCallbackResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_goofish_service_v1_goofish_proto protoreflect.FileDescriptor

const file_goofish_service_v1_goofish_proto_rawDesc = "" +
	"\n" +
	" goofish/service/v1/goofish.proto\x12\n" +
	"goofish.v1\"_\n" +
	"\x13PlatformInfoRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\"n\n" +
	"\x14PlatformInfoResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x120\n" +
	"\x04data\x18\x03 \x01(\v2\x1c.goofish.v1.PlatformInfoDataR\x04data\"*\n" +
	"\x10PlatformInfoData\x12\x16\n" +
	"\x06app_id\x18\x01 \x01(\x03R\x06app_id\"[\n" +
	"\x0fUserInfoRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\"f\n" +
	"\x10UserInfoResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12,\n" +
	"\x04data\x18\x03 \x01(\v2\x18.goofish.v1.UserInfoDataR\x04data\"(\n" +
	"\fUserInfoData\x12\x18\n" +
	"\abalance\x18\x01 \x01(\x03R\abalance\"a\n" +
	"\rGoodsTemplate\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04desc\x18\x03 \x01(\tR\x04desc\x12\x14\n" +
	"\x05check\x18\x04 \x01(\x05R\x05check\"\x86\x02\n" +
	"\vGoodsDetail\x12\x1a\n" +
	"\bgoods_no\x18\x01 \x01(\tR\bgoods_no\x12\x1e\n" +
	"\n" +
	"goods_type\x18\x02 \x01(\x05R\n" +
	"goods_type\x12\x1e\n" +
	"\n" +
	"goods_name\x18\x03 \x01(\tR\n" +
	"goods_name\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x03R\x05price\x12\x14\n" +
	"\x05stock\x18\x05 \x01(\x05R\x05stock\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\x12 \n" +
	"\vupdate_time\x18\a \x01(\x03R\vupdate_time\x125\n" +
	"\btemplate\x18\b \x03(\v2\x19.goofish.v1.GoodsTemplateR\btemplate\"\x9a\x01\n" +
	"\x12GoodsDetailRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\x12\x1e\n" +
	"\n" +
	"goods_type\x18\x04 \x01(\x05R\n" +
	"goods_type\x12\x1a\n" +
	"\bgoods_no\x18\x05 \x01(\tR\bgoods_no\"h\n" +
	"\x13GoodsDetailResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12+\n" +
	"\x04data\x18\x03 \x01(\v2\x17.goofish.v1.GoodsDetailR\x04data\"\xce\x01\n" +
	"\x10GoodsListRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\x12\x18\n" +
	"\akeyword\x18\x04 \x01(\tR\akeyword\x12\x1e\n" +
	"\n" +
	"goods_type\x18\x05 \x01(\x05R\n" +
	"goods_type\x12\x18\n" +
	"\apage_no\x18\x06 \x01(\x05R\apage_no\x12\x1c\n" +
	"\tpage_size\x18\a \x01(\x05R\tpage_size\"h\n" +
	"\x11GoodsListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12-\n" +
	"\x04data\x18\x03 \x01(\v2\x19.goofish.v1.GoodsListDataR\x04data\"R\n" +
	"\rGoodsListData\x12+\n" +
	"\x04list\x18\x01 \x03(\v2\x17.goofish.v1.GoodsDetailR\x04list\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"\xd9\x01\n" +
	"\x1bGoodsChangeSubscribeRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\x12\x1e\n" +
	"\n" +
	"goods_type\x18\x04 \x01(\x05R\n" +
	"goods_type\x12\x1a\n" +
	"\bgoods_no\x18\x05 \x01(\tR\bgoods_no\x12\x14\n" +
	"\x05token\x18\x06 \x01(\tR\x05token\x12\x1e\n" +
	"\n" +
	"notify_url\x18\a \x01(\tR\n" +
	"notify_url\"D\n" +
	"\x1cGoodsChangeSubscribeResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xbb\x01\n" +
	"\x1dGoodsChangeUnsubscribeRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\x12\x1e\n" +
	"\n" +
	"goods_type\x18\x04 \x01(\x05R\n" +
	"goods_type\x12\x1a\n" +
	"\bgoods_no\x18\x05 \x01(\tR\bgoods_no\x12\x14\n" +
	"\x05token\x18\x06 \x01(\tR\x05token\"F\n" +
	"\x1eGoodsChangeUnsubscribeResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x92\x01\n" +
	"\x1cGoodsChangeSubscribeListBody\x12\x1e\n" +
	"\n" +
	"goods_type\x18\x01 \x01(\x05R\n" +
	"goods_type\x12\x1a\n" +
	"\bgoods_no\x18\x02 \x01(\tR\bgoods_no\x12\x18\n" +
	"\apage_no\x18\x03 \x01(\x05R\apage_no\x12\x1c\n" +
	"\tpage_size\x18\x04 \x01(\x05R\tpage_size\"\xdf\x01\n" +
	"\x1fGoodsChangeSubscribeListRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\x12\x1e\n" +
	"\n" +
	"goods_type\x18\x04 \x01(\x05R\n" +
	"goods_type\x12\x1a\n" +
	"\bgoods_no\x18\x05 \x01(\tR\bgoods_no\x12\x18\n" +
	"\apage_no\x18\x06 \x01(\x05R\apage_no\x12\x1c\n" +
	"\tpage_size\x18\a \x01(\x05R\tpage_size\"\x86\x01\n" +
	" GoodsChangeSubscribeListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12<\n" +
	"\x04data\x18\x03 \x01(\v2(.goofish.v1.GoodsChangeSubscribeListDataR\x04data\"r\n" +
	"\x1cGoodsChangeSubscribeListData\x12<\n" +
	"\x04list\x18\x01 \x03(\v2(.goofish.v1.GoodsChangeSubscribeListItemR\x04list\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"\xb8\x01\n" +
	"\x1cGoodsChangeSubscribeListItem\x12\x1e\n" +
	"\n" +
	"goods_type\x18\x01 \x01(\x05R\n" +
	"goods_type\x12\x1a\n" +
	"\bgoods_no\x18\x02 \x01(\tR\bgoods_no\x12&\n" +
	"\x0esubscribe_time\x18\x03 \x01(\x03R\x0esubscribe_time\x12\x14\n" +
	"\x05token\x18\x04 \x01(\tR\x05token\x12\x1e\n" +
	"\n" +
	"notify_url\x18\x05 \x01(\tR\n" +
	"notify_url\"\xb5\x01\n" +
	"\x11GoodsCallbackItem\x12\x1a\n" +
	"\bgoods_no\x18\x01 \x01(\tR\bgoods_no\x12\x1e\n" +
	"\n" +
	"goods_type\x18\x02 \x01(\x05R\n" +
	"goods_type\x12\x14\n" +
	"\x05price\x18\x03 \x01(\x03R\x05price\x12\x14\n" +
	"\x05stock\x18\x04 \x01(\x05R\x05stock\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\x12 \n" +
	"\vchange_time\x18\x06 \x01(\x03R\vchange_time\"a\n" +
	"\x14GoodsCallbackRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x123\n" +
	"\x05items\x18\x02 \x03(\v2\x1d.goofish.v1.GoodsCallbackItemR\x05items\"=\n" +
	"\x15GoodsCallbackResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xde\x01\n" +
	"\n" +
	"BizContent\x12\x18\n" +
	"\aaccount\x18\x01 \x01(\tR\aaccount\x12\x1c\n" +
	"\tgame_name\x18\x02 \x01(\tR\tgame_name\x12\x1c\n" +
	"\tgame_role\x18\x03 \x01(\tR\tgame_role\x12\x1c\n" +
	"\tgame_area\x18\x04 \x01(\tR\tgame_area\x12 \n" +
	"\vgame_server\x18\x05 \x01(\tR\vgame_server\x12\x1a\n" +
	"\bbuyer_ip\x18\x06 \x01(\tR\bbuyer_ip\x12\x1e\n" +
	"\n" +
	"buyer_area\x18\a \x01(\tR\n" +
	"buyer_area\"@\n" +
	"\bCardItem\x12\x18\n" +
	"\acard_no\x18\x01 \x01(\tR\acard_no\x12\x1a\n" +
	"\bcard_pwd\x18\x02 \x01(\tR\bcard_pwd\"\xe0\x02\n" +
	"\x1aCreateRechargeOrderRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\x12\x1a\n" +
	"\border_no\x18\x04 \x01(\tR\border_no\x12\x1a\n" +
	"\bgoods_no\x18\x05 \x01(\tR\bgoods_no\x128\n" +
	"\vbiz_content\x18\x06 \x01(\v2\x16.goofish.v1.BizContentR\vbiz_content\x12\"\n" +
	"\fbuy_quantity\x18\a \x01(\x05R\fbuy_quantity\x12\x1e\n" +
	"\n" +
	"max_amount\x18\b \x01(\x03R\n" +
	"max_amount\x12\x1e\n" +
	"\n" +
	"notify_url\x18\t \x01(\tR\n" +
	"notify_url\x12\"\n" +
	"\fbiz_order_no\x18\n" +
	" \x01(\tR\fbiz_order_no\"v\n" +
	"\x1bCreateRechargeOrderResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x121\n" +
	"\x04data\x18\x03 \x01(\v2\x1d.goofish.v1.RechargeOrderDataR\x04data\"\x8f\x02\n" +
	"\x11RechargeOrderData\x12\x1a\n" +
	"\border_no\x18\x01 \x01(\tR\border_no\x12\"\n" +
	"\fout_order_no\x18\x02 \x01(\tR\fout_order_no\x12\"\n" +
	"\forder_status\x18\x03 \x01(\x05R\forder_status\x12\"\n" +
	"\forder_amount\x18\x04 \x01(\x03R\forder_amount\x12\x1e\n" +
	"\n" +
	"goods_name\x18\x05 \x01(\tR\n" +
	"goods_name\x12\x1e\n" +
	"\n" +
	"order_time\x18\x06 \x01(\x03R\n" +
	"order_time\x12\x1a\n" +
	"\bend_time\x18\a \x01(\x03R\bend_time\x12\x16\n" +
	"\x06remark\x18\b \x01(\tR\x06remark\"\xa2\x02\n" +
	"\x16CreateCardOrderRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\x12\x1a\n" +
	"\border_no\x18\x04 \x01(\tR\border_no\x12\x1a\n" +
	"\bgoods_no\x18\x05 \x01(\tR\bgoods_no\x12\"\n" +
	"\fbuy_quantity\x18\x06 \x01(\x05R\fbuy_quantity\x12\x1e\n" +
	"\n" +
	"max_amount\x18\a \x01(\x03R\n" +
	"max_amount\x12\x1e\n" +
	"\n" +
	"notify_url\x18\b \x01(\tR\n" +
	"notify_url\x12\"\n" +
	"\fbiz_order_no\x18\t \x01(\tR\fbiz_order_no\"n\n" +
	"\x17CreateCardOrderResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12-\n" +
	"\x04data\x18\x03 \x01(\v2\x19.goofish.v1.CardOrderDataR\x04data\"\xa1\x02\n" +
	"\rCardOrderData\x12\x1a\n" +
	"\border_no\x18\x01 \x01(\tR\border_no\x12\"\n" +
	"\fout_order_no\x18\x02 \x01(\tR\fout_order_no\x12\"\n" +
	"\forder_status\x18\x03 \x01(\x05R\forder_status\x12\"\n" +
	"\forder_amount\x18\x04 \x01(\x03R\forder_amount\x12\x1e\n" +
	"\n" +
	"order_time\x18\x05 \x01(\x03R\n" +
	"order_time\x12\x1a\n" +
	"\bend_time\x18\x06 \x01(\x03R\bend_time\x124\n" +
	"\n" +
	"card_items\x18\a \x03(\v2\x14.goofish.v1.CardItemR\n" +
	"card_items\x12\x16\n" +
	"\x06remark\x18\b \x01(\tR\x06remark\"\xbe\x01\n" +
	"\x12OrderDetailRequest\x12\x16\n" +
	"\x06mch_id\x18\x01 \x01(\tR\x06mch_id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x12\n" +
	"\x04sign\x18\x03 \x01(\tR\x04sign\x12\x1e\n" +
	"\n" +
	"order_type\x18\x04 \x01(\x05R\n" +
	"order_type\x12\x1a\n" +
	"\border_no\x18\x05 \x01(\tR\border_no\x12\"\n" +
	"\fout_order_no\x18\x06 \x01(\tR\fout_order_no\"l\n" +
	"\x13OrderDetailResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12/\n" +
	"\x04data\x18\x03 \x01(\v2\x1b.goofish.v1.OrderDetailDataR\x04data\"\xdd\x03\n" +
	"\x0fOrderDetailData\x12\x1e\n" +
	"\n" +
	"order_type\x18\x01 \x01(\x05R\n" +
	"order_type\x12\x1a\n" +
	"\border_no\x18\x02 \x01(\tR\border_no\x12\"\n" +
	"\fout_order_no\x18\x03 \x01(\tR\fout_order_no\x12\"\n" +
	"\forder_status\x18\x04 \x01(\x05R\forder_status\x12\"\n" +
	"\forder_amount\x18\x05 \x01(\x03R\forder_amount\x12\x1a\n" +
	"\bgoods_no\x18\x06 \x01(\tR\bgoods_no\x12\x1e\n" +
	"\n" +
	"goods_name\x18\a \x01(\tR\n" +
	"goods_name\x12\"\n" +
	"\fbuy_quantity\x18\b \x01(\x05R\fbuy_quantity\x12\x1e\n" +
	"\n" +
	"order_time\x18\t \x01(\x03R\n" +
	"order_time\x12\x1a\n" +
	"\bend_time\x18\n" +
	" \x01(\x03R\bend_time\x128\n" +
	"\vbiz_content\x18\v \x01(\v2\x16.goofish.v1.BizContentR\vbiz_content\x124\n" +
	"\n" +
	"card_items\x18\f \x03(\v2\x14.goofish.v1.CardItemR\n" +
	"card_items\x12\x16\n" +
	"\x06remark\x18\r \x01(\tR\x06remark\"\x9a\x02\n" +
	"\x14OrderCallbackRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x1e\n" +
	"\n" +
	"order_type\x18\x02 \x01(\x05R\n" +
	"order_type\x12\x1a\n" +
	"\border_no\x18\x03 \x01(\tR\border_no\x12\"\n" +
	"\fout_order_no\x18\x04 \x01(\tR\fout_order_no\x12\"\n" +
	"\forder_status\x18\x05 \x01(\x05R\forder_status\x12\x1a\n" +
	"\bend_time\x18\x06 \x01(\x03R\bend_time\x124\n" +
	"\n" +
	"card_items\x18\a \x03(\v2\x14.goofish.v1.CardItemR\n" +
	"card_items\x12\x16\n" +
	"\x06remark\x18\b \x01(\tR\x06remark\"=\n" +
	"\x15OrderCallbackResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg2\xe9\b\n" +
	"\x0eGoofishService\x12T\n" +
	"\x0fGetPlatformInfo\x12\x1f.goofish.v1.PlatformInfoRequest\x1a .goofish.v1.PlatformInfoResponse\x12H\n" +
	"\vGetUserInfo\x12\x1b.goofish.v1.UserInfoRequest\x1a\x1c.goofish.v1.UserInfoResponse\x12Q\n" +
	"\x0eGetGoodsDetail\x12\x1e.goofish.v1.GoodsDetailRequest\x1a\x1f.goofish.v1.GoodsDetailResponse\x12K\n" +
	"\fGetGoodsList\x12\x1c.goofish.v1.GoodsListRequest\x1a\x1d.goofish.v1.GoodsListResponse\x12x\n" +
	"\x1bGetGoodsChangeSubscribeList\x12+.goofish.v1.GoodsChangeSubscribeListRequest\x1a,.goofish.v1.GoodsChangeSubscribeListResponse\x12i\n" +
	"\x14GoodsChangeSubscribe\x12'.goofish.v1.GoodsChangeSubscribeRequest\x1a(.goofish.v1.GoodsChangeSubscribeResponse\x12o\n" +
	"\x16GoodsChangeUnsubscribe\x12).goofish.v1.GoodsChangeUnsubscribeRequest\x1a*.goofish.v1.GoodsChangeUnsubscribeResponse\x12T\n" +
	"\rGoodsCallback\x12 .goofish.v1.GoodsCallbackRequest\x1a!.goofish.v1.GoodsCallbackResponse\x12f\n" +
	"\x13CreateRechargeOrder\x12&.goofish.v1.CreateRechargeOrderRequest\x1a'.goofish.v1.CreateRechargeOrderResponse\x12Z\n" +
	"\x0fCreateCardOrder\x12\".goofish.v1.CreateCardOrderRequest\x1a#.goofish.v1.CreateCardOrderResponse\x12Q\n" +
	"\x0eGetOrderDetail\x12\x1e.goofish.v1.OrderDetailRequest\x1a\x1f.goofish.v1.OrderDetailResponse\x12T\n" +
	"\rOrderCallback\x12 .goofish.v1.OrderCallbackRequest\x1a!.goofish.v1.OrderCallbackResponseB\x9d\x01\n" +
	"\x0ecom.goofish.v1B\fGoofishProtoP\x01Z4kratos-admin/api/gen/go/goofish/service/v1;goofishv1\xa2\x02\x03GXX\xaa\x02\n" +
	"Goofish.V1\xca\x02\n" +
	"Goofish\\V1\xe2\x02\x16Goofish\\V1\\GPBMetadata\xea\x02\vGoofish::V1b\x06proto3"

var (
	file_goofish_service_v1_goofish_proto_rawDescOnce sync.Once
	file_goofish_service_v1_goofish_proto_rawDescData []byte
)

func file_goofish_service_v1_goofish_proto_rawDescGZIP() []byte {
	file_goofish_service_v1_goofish_proto_rawDescOnce.Do(func() {
		file_goofish_service_v1_goofish_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_goofish_service_v1_goofish_proto_rawDesc), len(file_goofish_service_v1_goofish_proto_rawDesc)))
	})
	return file_goofish_service_v1_goofish_proto_rawDescData
}

var file_goofish_service_v1_goofish_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_goofish_service_v1_goofish_proto_goTypes = []any{
	(*PlatformInfoRequest)(nil),              // 0: goofish.v1.PlatformInfoRequest
	(*PlatformInfoResponse)(nil),             // 1: goofish.v1.PlatformInfoResponse
	(*PlatformInfoData)(nil),                 // 2: goofish.v1.PlatformInfoData
	(*UserInfoRequest)(nil),                  // 3: goofish.v1.UserInfoRequest
	(*UserInfoResponse)(nil),                 // 4: goofish.v1.UserInfoResponse
	(*UserInfoData)(nil),                     // 5: goofish.v1.UserInfoData
	(*GoodsTemplate)(nil),                    // 6: goofish.v1.GoodsTemplate
	(*GoodsDetail)(nil),                      // 7: goofish.v1.GoodsDetail
	(*GoodsDetailRequest)(nil),               // 8: goofish.v1.GoodsDetailRequest
	(*GoodsDetailResponse)(nil),              // 9: goofish.v1.GoodsDetailResponse
	(*GoodsListRequest)(nil),                 // 10: goofish.v1.GoodsListRequest
	(*GoodsListResponse)(nil),                // 11: goofish.v1.GoodsListResponse
	(*GoodsListData)(nil),                    // 12: goofish.v1.GoodsListData
	(*GoodsChangeSubscribeRequest)(nil),      // 13: goofish.v1.GoodsChangeSubscribeRequest
	(*GoodsChangeSubscribeResponse)(nil),     // 14: goofish.v1.GoodsChangeSubscribeResponse
	(*GoodsChangeUnsubscribeRequest)(nil),    // 15: goofish.v1.GoodsChangeUnsubscribeRequest
	(*GoodsChangeUnsubscribeResponse)(nil),   // 16: goofish.v1.GoodsChangeUnsubscribeResponse
	(*GoodsChangeSubscribeListBody)(nil),     // 17: goofish.v1.GoodsChangeSubscribeListBody
	(*GoodsChangeSubscribeListRequest)(nil),  // 18: goofish.v1.GoodsChangeSubscribeListRequest
	(*GoodsChangeSubscribeListResponse)(nil), // 19: goofish.v1.GoodsChangeSubscribeListResponse
	(*GoodsChangeSubscribeListData)(nil),     // 20: goofish.v1.GoodsChangeSubscribeListData
	(*GoodsChangeSubscribeListItem)(nil),     // 21: goofish.v1.GoodsChangeSubscribeListItem
	(*GoodsCallbackItem)(nil),                // 22: goofish.v1.GoodsCallbackItem
	(*GoodsCallbackRequest)(nil),             // 23: goofish.v1.GoodsCallbackRequest
	(*GoodsCallbackResponse)(nil),            // 24: goofish.v1.GoodsCallbackResponse
	(*BizContent)(nil),                       // 25: goofish.v1.BizContent
	(*CardItem)(nil),                         // 26: goofish.v1.CardItem
	(*CreateRechargeOrderRequest)(nil),       // 27: goofish.v1.CreateRechargeOrderRequest
	(*CreateRechargeOrderResponse)(nil),      // 28: goofish.v1.CreateRechargeOrderResponse
	(*RechargeOrderData)(nil),                // 29: goofish.v1.RechargeOrderData
	(*CreateCardOrderRequest)(nil),           // 30: goofish.v1.CreateCardOrderRequest
	(*CreateCardOrderResponse)(nil),          // 31: goofish.v1.CreateCardOrderResponse
	(*CardOrderData)(nil),                    // 32: goofish.v1.CardOrderData
	(*OrderDetailRequest)(nil),               // 33: goofish.v1.OrderDetailRequest
	(*OrderDetailResponse)(nil),              // 34: goofish.v1.OrderDetailResponse
	(*OrderDetailData)(nil),                  // 35: goofish.v1.OrderDetailData
	(*OrderCallbackRequest)(nil),             // 36: goofish.v1.OrderCallbackRequest
	(*OrderCallbackResponse)(nil),            // 37: goofish.v1.OrderCallbackResponse
}
var file_goofish_service_v1_goofish_proto_depIdxs = []int32{
	2,  // 0: goofish.v1.PlatformInfoResponse.data:type_name -> goofish.v1.PlatformInfoData
	5,  // 1: goofish.v1.UserInfoResponse.data:type_name -> goofish.v1.UserInfoData
	6,  // 2: goofish.v1.GoodsDetail.template:type_name -> goofish.v1.GoodsTemplate
	7,  // 3: goofish.v1.GoodsDetailResponse.data:type_name -> goofish.v1.GoodsDetail
	12, // 4: goofish.v1.GoodsListResponse.data:type_name -> goofish.v1.GoodsListData
	7,  // 5: goofish.v1.GoodsListData.list:type_name -> goofish.v1.GoodsDetail
	20, // 6: goofish.v1.GoodsChangeSubscribeListResponse.data:type_name -> goofish.v1.GoodsChangeSubscribeListData
	21, // 7: goofish.v1.GoodsChangeSubscribeListData.list:type_name -> goofish.v1.GoodsChangeSubscribeListItem
	22, // 8: goofish.v1.GoodsCallbackRequest.items:type_name -> goofish.v1.GoodsCallbackItem
	25, // 9: goofish.v1.CreateRechargeOrderRequest.biz_content:type_name -> goofish.v1.BizContent
	29, // 10: goofish.v1.CreateRechargeOrderResponse.data:type_name -> goofish.v1.RechargeOrderData
	32, // 11: goofish.v1.CreateCardOrderResponse.data:type_name -> goofish.v1.CardOrderData
	26, // 12: goofish.v1.CardOrderData.card_items:type_name -> goofish.v1.CardItem
	35, // 13: goofish.v1.OrderDetailResponse.data:type_name -> goofish.v1.OrderDetailData
	25, // 14: goofish.v1.OrderDetailData.biz_content:type_name -> goofish.v1.BizContent
	26, // 15: goofish.v1.OrderDetailData.card_items:type_name -> goofish.v1.CardItem
	26, // 16: goofish.v1.OrderCallbackRequest.card_items:type_name -> goofish.v1.CardItem
	0,  // 17: goofish.v1.GoofishService.GetPlatformInfo:input_type -> goofish.v1.PlatformInfoRequest
	3,  // 18: goofish.v1.GoofishService.GetUserInfo:input_type -> goofish.v1.UserInfoRequest
	8,  // 19: goofish.v1.GoofishService.GetGoodsDetail:input_type -> goofish.v1.GoodsDetailRequest
	10, // 20: goofish.v1.GoofishService.GetGoodsList:input_type -> goofish.v1.GoodsListRequest
	18, // 21: goofish.v1.GoofishService.GetGoodsChangeSubscribeList:input_type -> goofish.v1.GoodsChangeSubscribeListRequest
	13, // 22: goofish.v1.GoofishService.GoodsChangeSubscribe:input_type -> goofish.v1.GoodsChangeSubscribeRequest
	15, // 23: goofish.v1.GoofishService.GoodsChangeUnsubscribe:input_type -> goofish.v1.GoodsChangeUnsubscribeRequest
	23, // 24: goofish.v1.GoofishService.GoodsCallback:input_type -> goofish.v1.GoodsCallbackRequest
	27, // 25: goofish.v1.GoofishService.CreateRechargeOrder:input_type -> goofish.v1.CreateRechargeOrderRequest
	30, // 26: goofish.v1.GoofishService.CreateCardOrder:input_type -> goofish.v1.CreateCardOrderRequest
	33, // 27: goofish.v1.GoofishService.GetOrderDetail:input_type -> goofish.v1.OrderDetailRequest
	36, // 28: goofish.v1.GoofishService.OrderCallback:input_type -> goofish.v1.OrderCallbackRequest
	1,  // 29: goofish.v1.GoofishService.GetPlatformInfo:output_type -> goofish.v1.PlatformInfoResponse
	4,  // 30: goofish.v1.GoofishService.GetUserInfo:output_type -> goofish.v1.UserInfoResponse
	9,  // 31: goofish.v1.GoofishService.GetGoodsDetail:output_type -> goofish.v1.GoodsDetailResponse
	11, // 32: goofish.v1.GoofishService.GetGoodsList:output_type -> goofish.v1.GoodsListResponse
	19, // 33: goofish.v1.GoofishService.GetGoodsChangeSubscribeList:output_type -> goofish.v1.GoodsChangeSubscribeListResponse
	14, // 34: goofish.v1.GoofishService.GoodsChangeSubscribe:output_type -> goofish.v1.GoodsChangeSubscribeResponse
	16, // 35: goofish.v1.GoofishService.GoodsChangeUnsubscribe:output_type -> goofish.v1.GoodsChangeUnsubscribeResponse
	24, // 36: goofish.v1.GoofishService.GoodsCallback:output_type -> goofish.v1.GoodsCallbackResponse
	28, // 37: goofish.v1.GoofishService.CreateRechargeOrder:output_type -> goofish.v1.CreateRechargeOrderResponse
	31, // 38: goofish.v1.GoofishService.CreateCardOrder:output_type -> goofish.v1.CreateCardOrderResponse
	34, // 39: goofish.v1.GoofishService.GetOrderDetail:output_type -> goofish.v1.OrderDetailResponse
	37, // 40: goofish.v1.GoofishService.OrderCallback:output_type -> goofish.v1.OrderCallbackResponse
	29, // [29:41] is the sub-list for method output_type
	17, // [17:29] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_goofish_service_v1_goofish_proto_init() }
func file_goofish_service_v1_goofish_proto_init() {
	if File_goofish_service_v1_goofish_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_goofish_service_v1_goofish_proto_rawDesc), len(file_goofish_service_v1_goofish_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_goofish_service_v1_goofish_proto_goTypes,
		DependencyIndexes: file_goofish_service_v1_goofish_proto_depIdxs,
		MessageInfos:      file_goofish_service_v1_goofish_proto_msgTypes,
	}.Build()
	File_goofish_service_v1_goofish_proto = out.File
	file_goofish_service_v1_goofish_proto_goTypes = nil
	file_goofish_service_v1_goofish_proto_depIdxs = nil
}
