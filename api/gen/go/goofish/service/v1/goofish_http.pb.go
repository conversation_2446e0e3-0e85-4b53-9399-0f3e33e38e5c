// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: goofish/service/v1/goofish_http.proto

package goofishv1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_goofish_service_v1_goofish_http_proto protoreflect.FileDescriptor

const file_goofish_service_v1_goofish_http_proto_rawDesc = "" +
	"\n" +
	"%goofish/service/v1/goofish_http.proto\x12\n" +
	"goofish.v1\x1a goofish/service/v1/goofish.proto\x1a\x1cgoogle/api/annotations.proto2\xa2\f\n" +
	"\x12GoofishHttpService\x12v\n" +
	"\x0fGetPlatformInfo\x12\x1f.goofish.v1.PlatformInfoRequest\x1a .goofish.v1.PlatformInfoResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/api/v1/platform/info\x12f\n" +
	"\vGetUserInfo\x12\x1b.goofish.v1.UserInfoRequest\x1a\x1c.goofish.v1.UserInfoResponse\"\x1c\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/api/v1/user/info\x12r\n" +
	"\x0eGetGoodsDetail\x12\x1e.goofish.v1.GoodsDetailRequest\x1a\x1f.goofish.v1.GoodsDetailResponse\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/api/v1/goods/detail\x12j\n" +
	"\fGetGoodsList\x12\x1c.goofish.v1.GoodsListRequest\x1a\x1d.goofish.v1.GoodsListResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/api/v1/goods/list\x12\xa8\x01\n" +
	"\x1bGetGoodsChangeSubscribeList\x12+.goofish.v1.GoodsChangeSubscribeListRequest\x1a,.goofish.v1.GoodsChangeSubscribeListResponse\".\x82\xd3\xe4\x93\x02(:\x01*\"#/api/v1/goods/change/subscribe/list\x12\x94\x01\n" +
	"\x14GoodsChangeSubscribe\x12'.goofish.v1.GoodsChangeSubscribeRequest\x1a(.goofish.v1.GoodsChangeSubscribeResponse\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/api/v1/goods/change/subscribe\x12\x9c\x01\n" +
	"\x16GoodsChangeUnsubscribe\x12).goofish.v1.GoodsChangeUnsubscribeRequest\x1a*.goofish.v1.GoodsChangeUnsubscribeResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\" /api/v1/goods/change/unsubscribe\x12w\n" +
	"\rGoodsCallback\x12 .goofish.v1.GoodsCallbackRequest\x1a!.goofish.v1.GoodsCallbackResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/api/v1/goods/callback\x12\x89\x01\n" +
	"\x13CreateRechargeOrder\x12&.goofish.v1.CreateRechargeOrderRequest\x1a'.goofish.v1.CreateRechargeOrderResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/api/v1/order/recharge\x12y\n" +
	"\x0fCreateCardOrder\x12\".goofish.v1.CreateCardOrderRequest\x1a#.goofish.v1.CreateCardOrderResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/api/v1/order/card\x12r\n" +
	"\x0eGetOrderDetail\x12\x1e.goofish.v1.OrderDetailRequest\x1a\x1f.goofish.v1.OrderDetailResponse\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/api/v1/order/detail\x12w\n" +
	"\rOrderCallback\x12 .goofish.v1.OrderCallbackRequest\x1a!.goofish.v1.OrderCallbackResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/api/v1/order/callbackB\xa1\x01\n" +
	"\x0ecom.goofish.v1B\x10GoofishHttpProtoP\x01Z4kratos-admin/api/gen/go/goofish/service/v1;goofishv1\xa2\x02\x03GXX\xaa\x02\n" +
	"Goofish.V1\xca\x02\n" +
	"Goofish\\V1\xe2\x02\x16Goofish\\V1\\GPBMetadata\xea\x02\vGoofish::V1b\x06proto3"

var file_goofish_service_v1_goofish_http_proto_goTypes = []any{
	(*PlatformInfoRequest)(nil),              // 0: goofish.v1.PlatformInfoRequest
	(*UserInfoRequest)(nil),                  // 1: goofish.v1.UserInfoRequest
	(*GoodsDetailRequest)(nil),               // 2: goofish.v1.GoodsDetailRequest
	(*GoodsListRequest)(nil),                 // 3: goofish.v1.GoodsListRequest
	(*GoodsChangeSubscribeListRequest)(nil),  // 4: goofish.v1.GoodsChangeSubscribeListRequest
	(*GoodsChangeSubscribeRequest)(nil),      // 5: goofish.v1.GoodsChangeSubscribeRequest
	(*GoodsChangeUnsubscribeRequest)(nil),    // 6: goofish.v1.GoodsChangeUnsubscribeRequest
	(*GoodsCallbackRequest)(nil),             // 7: goofish.v1.GoodsCallbackRequest
	(*CreateRechargeOrderRequest)(nil),       // 8: goofish.v1.CreateRechargeOrderRequest
	(*CreateCardOrderRequest)(nil),           // 9: goofish.v1.CreateCardOrderRequest
	(*OrderDetailRequest)(nil),               // 10: goofish.v1.OrderDetailRequest
	(*OrderCallbackRequest)(nil),             // 11: goofish.v1.OrderCallbackRequest
	(*PlatformInfoResponse)(nil),             // 12: goofish.v1.PlatformInfoResponse
	(*UserInfoResponse)(nil),                 // 13: goofish.v1.UserInfoResponse
	(*GoodsDetailResponse)(nil),              // 14: goofish.v1.GoodsDetailResponse
	(*GoodsListResponse)(nil),                // 15: goofish.v1.GoodsListResponse
	(*GoodsChangeSubscribeListResponse)(nil), // 16: goofish.v1.GoodsChangeSubscribeListResponse
	(*GoodsChangeSubscribeResponse)(nil),     // 17: goofish.v1.GoodsChangeSubscribeResponse
	(*GoodsChangeUnsubscribeResponse)(nil),   // 18: goofish.v1.GoodsChangeUnsubscribeResponse
	(*GoodsCallbackResponse)(nil),            // 19: goofish.v1.GoodsCallbackResponse
	(*CreateRechargeOrderResponse)(nil),      // 20: goofish.v1.CreateRechargeOrderResponse
	(*CreateCardOrderResponse)(nil),          // 21: goofish.v1.CreateCardOrderResponse
	(*OrderDetailResponse)(nil),              // 22: goofish.v1.OrderDetailResponse
	(*OrderCallbackResponse)(nil),            // 23: goofish.v1.OrderCallbackResponse
}
var file_goofish_service_v1_goofish_http_proto_depIdxs = []int32{
	0,  // 0: goofish.v1.GoofishHttpService.GetPlatformInfo:input_type -> goofish.v1.PlatformInfoRequest
	1,  // 1: goofish.v1.GoofishHttpService.GetUserInfo:input_type -> goofish.v1.UserInfoRequest
	2,  // 2: goofish.v1.GoofishHttpService.GetGoodsDetail:input_type -> goofish.v1.GoodsDetailRequest
	3,  // 3: goofish.v1.GoofishHttpService.GetGoodsList:input_type -> goofish.v1.GoodsListRequest
	4,  // 4: goofish.v1.GoofishHttpService.GetGoodsChangeSubscribeList:input_type -> goofish.v1.GoodsChangeSubscribeListRequest
	5,  // 5: goofish.v1.GoofishHttpService.GoodsChangeSubscribe:input_type -> goofish.v1.GoodsChangeSubscribeRequest
	6,  // 6: goofish.v1.GoofishHttpService.GoodsChangeUnsubscribe:input_type -> goofish.v1.GoodsChangeUnsubscribeRequest
	7,  // 7: goofish.v1.GoofishHttpService.GoodsCallback:input_type -> goofish.v1.GoodsCallbackRequest
	8,  // 8: goofish.v1.GoofishHttpService.CreateRechargeOrder:input_type -> goofish.v1.CreateRechargeOrderRequest
	9,  // 9: goofish.v1.GoofishHttpService.CreateCardOrder:input_type -> goofish.v1.CreateCardOrderRequest
	10, // 10: goofish.v1.GoofishHttpService.GetOrderDetail:input_type -> goofish.v1.OrderDetailRequest
	11, // 11: goofish.v1.GoofishHttpService.OrderCallback:input_type -> goofish.v1.OrderCallbackRequest
	12, // 12: goofish.v1.GoofishHttpService.GetPlatformInfo:output_type -> goofish.v1.PlatformInfoResponse
	13, // 13: goofish.v1.GoofishHttpService.GetUserInfo:output_type -> goofish.v1.UserInfoResponse
	14, // 14: goofish.v1.GoofishHttpService.GetGoodsDetail:output_type -> goofish.v1.GoodsDetailResponse
	15, // 15: goofish.v1.GoofishHttpService.GetGoodsList:output_type -> goofish.v1.GoodsListResponse
	16, // 16: goofish.v1.GoofishHttpService.GetGoodsChangeSubscribeList:output_type -> goofish.v1.GoodsChangeSubscribeListResponse
	17, // 17: goofish.v1.GoofishHttpService.GoodsChangeSubscribe:output_type -> goofish.v1.GoodsChangeSubscribeResponse
	18, // 18: goofish.v1.GoofishHttpService.GoodsChangeUnsubscribe:output_type -> goofish.v1.GoodsChangeUnsubscribeResponse
	19, // 19: goofish.v1.GoofishHttpService.GoodsCallback:output_type -> goofish.v1.GoodsCallbackResponse
	20, // 20: goofish.v1.GoofishHttpService.CreateRechargeOrder:output_type -> goofish.v1.CreateRechargeOrderResponse
	21, // 21: goofish.v1.GoofishHttpService.CreateCardOrder:output_type -> goofish.v1.CreateCardOrderResponse
	22, // 22: goofish.v1.GoofishHttpService.GetOrderDetail:output_type -> goofish.v1.OrderDetailResponse
	23, // 23: goofish.v1.GoofishHttpService.OrderCallback:output_type -> goofish.v1.OrderCallbackResponse
	12, // [12:24] is the sub-list for method output_type
	0,  // [0:12] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_goofish_service_v1_goofish_http_proto_init() }
func file_goofish_service_v1_goofish_http_proto_init() {
	if File_goofish_service_v1_goofish_http_proto != nil {
		return
	}
	file_goofish_service_v1_goofish_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_goofish_service_v1_goofish_http_proto_rawDesc), len(file_goofish_service_v1_goofish_http_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_goofish_service_v1_goofish_http_proto_goTypes,
		DependencyIndexes: file_goofish_service_v1_goofish_http_proto_depIdxs,
	}.Build()
	File_goofish_service_v1_goofish_http_proto = out.File
	file_goofish_service_v1_goofish_http_proto_goTypes = nil
	file_goofish_service_v1_goofish_http_proto_depIdxs = nil
}
