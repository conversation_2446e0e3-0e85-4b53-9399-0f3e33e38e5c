// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: goofish/service/v1/goofish_http.proto

package goofishv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GoofishHttpService_GetPlatformInfo_FullMethodName             = "/goofish.v1.GoofishHttpService/GetPlatformInfo"
	GoofishHttpService_GetUserInfo_FullMethodName                 = "/goofish.v1.GoofishHttpService/GetUserInfo"
	GoofishHttpService_GetGoodsDetail_FullMethodName              = "/goofish.v1.GoofishHttpService/GetGoodsDetail"
	GoofishHttpService_GetGoodsList_FullMethodName                = "/goofish.v1.GoofishHttpService/GetGoodsList"
	GoofishHttpService_GetGoodsChangeSubscribeList_FullMethodName = "/goofish.v1.GoofishHttpService/GetGoodsChangeSubscribeList"
	GoofishHttpService_GoodsChangeSubscribe_FullMethodName        = "/goofish.v1.GoofishHttpService/GoodsChangeSubscribe"
	GoofishHttpService_GoodsChangeUnsubscribe_FullMethodName      = "/goofish.v1.GoofishHttpService/GoodsChangeUnsubscribe"
	GoofishHttpService_GoodsCallback_FullMethodName               = "/goofish.v1.GoofishHttpService/GoodsCallback"
	GoofishHttpService_CreateRechargeOrder_FullMethodName         = "/goofish.v1.GoofishHttpService/CreateRechargeOrder"
	GoofishHttpService_CreateCardOrder_FullMethodName             = "/goofish.v1.GoofishHttpService/CreateCardOrder"
	GoofishHttpService_GetOrderDetail_FullMethodName              = "/goofish.v1.GoofishHttpService/GetOrderDetail"
	GoofishHttpService_OrderCallback_FullMethodName               = "/goofish.v1.GoofishHttpService/OrderCallback"
)

// GoofishHttpServiceClient is the client API for GoofishHttpService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// goofish HTTP API 服务定义
type GoofishHttpServiceClient interface {
	// 查询平台信息
	GetPlatformInfo(ctx context.Context, in *PlatformInfoRequest, opts ...grpc.CallOption) (*PlatformInfoResponse, error)
	// 查询商户信息
	GetUserInfo(ctx context.Context, in *UserInfoRequest, opts ...grpc.CallOption) (*UserInfoResponse, error)
	// 查询商品详情
	GetGoodsDetail(ctx context.Context, in *GoodsDetailRequest, opts ...grpc.CallOption) (*GoodsDetailResponse, error)
	// 查询商品列表
	GetGoodsList(ctx context.Context, in *GoodsListRequest, opts ...grpc.CallOption) (*GoodsListResponse, error)
	// 查询商品订阅列表
	GetGoodsChangeSubscribeList(ctx context.Context, in *GoodsChangeSubscribeListRequest, opts ...grpc.CallOption) (*GoodsChangeSubscribeListResponse, error)
	// 订阅商品变更通知
	GoodsChangeSubscribe(ctx context.Context, in *GoodsChangeSubscribeRequest, opts ...grpc.CallOption) (*GoodsChangeSubscribeResponse, error)
	// 取消商品变更通知
	GoodsChangeUnsubscribe(ctx context.Context, in *GoodsChangeUnsubscribeRequest, opts ...grpc.CallOption) (*GoodsChangeUnsubscribeResponse, error)
	// 商品回调通知
	GoodsCallback(ctx context.Context, in *GoodsCallbackRequest, opts ...grpc.CallOption) (*GoodsCallbackResponse, error)
	// 创建直充订单
	CreateRechargeOrder(ctx context.Context, in *CreateRechargeOrderRequest, opts ...grpc.CallOption) (*CreateRechargeOrderResponse, error)
	// 创建卡密订单
	CreateCardOrder(ctx context.Context, in *CreateCardOrderRequest, opts ...grpc.CallOption) (*CreateCardOrderResponse, error)
	// 查询订单详情
	GetOrderDetail(ctx context.Context, in *OrderDetailRequest, opts ...grpc.CallOption) (*OrderDetailResponse, error)
	// 订单回调通知
	OrderCallback(ctx context.Context, in *OrderCallbackRequest, opts ...grpc.CallOption) (*OrderCallbackResponse, error)
}

type goofishHttpServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGoofishHttpServiceClient(cc grpc.ClientConnInterface) GoofishHttpServiceClient {
	return &goofishHttpServiceClient{cc}
}

func (c *goofishHttpServiceClient) GetPlatformInfo(ctx context.Context, in *PlatformInfoRequest, opts ...grpc.CallOption) (*PlatformInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlatformInfoResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_GetPlatformInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) GetUserInfo(ctx context.Context, in *UserInfoRequest, opts ...grpc.CallOption) (*UserInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserInfoResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_GetUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) GetGoodsDetail(ctx context.Context, in *GoodsDetailRequest, opts ...grpc.CallOption) (*GoodsDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GoodsDetailResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_GetGoodsDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) GetGoodsList(ctx context.Context, in *GoodsListRequest, opts ...grpc.CallOption) (*GoodsListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GoodsListResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_GetGoodsList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) GetGoodsChangeSubscribeList(ctx context.Context, in *GoodsChangeSubscribeListRequest, opts ...grpc.CallOption) (*GoodsChangeSubscribeListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GoodsChangeSubscribeListResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_GetGoodsChangeSubscribeList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) GoodsChangeSubscribe(ctx context.Context, in *GoodsChangeSubscribeRequest, opts ...grpc.CallOption) (*GoodsChangeSubscribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GoodsChangeSubscribeResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_GoodsChangeSubscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) GoodsChangeUnsubscribe(ctx context.Context, in *GoodsChangeUnsubscribeRequest, opts ...grpc.CallOption) (*GoodsChangeUnsubscribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GoodsChangeUnsubscribeResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_GoodsChangeUnsubscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) GoodsCallback(ctx context.Context, in *GoodsCallbackRequest, opts ...grpc.CallOption) (*GoodsCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GoodsCallbackResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_GoodsCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) CreateRechargeOrder(ctx context.Context, in *CreateRechargeOrderRequest, opts ...grpc.CallOption) (*CreateRechargeOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRechargeOrderResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_CreateRechargeOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) CreateCardOrder(ctx context.Context, in *CreateCardOrderRequest, opts ...grpc.CallOption) (*CreateCardOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCardOrderResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_CreateCardOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) GetOrderDetail(ctx context.Context, in *OrderDetailRequest, opts ...grpc.CallOption) (*OrderDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderDetailResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_GetOrderDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goofishHttpServiceClient) OrderCallback(ctx context.Context, in *OrderCallbackRequest, opts ...grpc.CallOption) (*OrderCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderCallbackResponse)
	err := c.cc.Invoke(ctx, GoofishHttpService_OrderCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GoofishHttpServiceServer is the server API for GoofishHttpService service.
// All implementations must embed UnimplementedGoofishHttpServiceServer
// for forward compatibility.
//
// goofish HTTP API 服务定义
type GoofishHttpServiceServer interface {
	// 查询平台信息
	GetPlatformInfo(context.Context, *PlatformInfoRequest) (*PlatformInfoResponse, error)
	// 查询商户信息
	GetUserInfo(context.Context, *UserInfoRequest) (*UserInfoResponse, error)
	// 查询商品详情
	GetGoodsDetail(context.Context, *GoodsDetailRequest) (*GoodsDetailResponse, error)
	// 查询商品列表
	GetGoodsList(context.Context, *GoodsListRequest) (*GoodsListResponse, error)
	// 查询商品订阅列表
	GetGoodsChangeSubscribeList(context.Context, *GoodsChangeSubscribeListRequest) (*GoodsChangeSubscribeListResponse, error)
	// 订阅商品变更通知
	GoodsChangeSubscribe(context.Context, *GoodsChangeSubscribeRequest) (*GoodsChangeSubscribeResponse, error)
	// 取消商品变更通知
	GoodsChangeUnsubscribe(context.Context, *GoodsChangeUnsubscribeRequest) (*GoodsChangeUnsubscribeResponse, error)
	// 商品回调通知
	GoodsCallback(context.Context, *GoodsCallbackRequest) (*GoodsCallbackResponse, error)
	// 创建直充订单
	CreateRechargeOrder(context.Context, *CreateRechargeOrderRequest) (*CreateRechargeOrderResponse, error)
	// 创建卡密订单
	CreateCardOrder(context.Context, *CreateCardOrderRequest) (*CreateCardOrderResponse, error)
	// 查询订单详情
	GetOrderDetail(context.Context, *OrderDetailRequest) (*OrderDetailResponse, error)
	// 订单回调通知
	OrderCallback(context.Context, *OrderCallbackRequest) (*OrderCallbackResponse, error)
	mustEmbedUnimplementedGoofishHttpServiceServer()
}

// UnimplementedGoofishHttpServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGoofishHttpServiceServer struct{}

func (UnimplementedGoofishHttpServiceServer) GetPlatformInfo(context.Context, *PlatformInfoRequest) (*PlatformInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlatformInfo not implemented")
}
func (UnimplementedGoofishHttpServiceServer) GetUserInfo(context.Context, *UserInfoRequest) (*UserInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (UnimplementedGoofishHttpServiceServer) GetGoodsDetail(context.Context, *GoodsDetailRequest) (*GoodsDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsDetail not implemented")
}
func (UnimplementedGoofishHttpServiceServer) GetGoodsList(context.Context, *GoodsListRequest) (*GoodsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsList not implemented")
}
func (UnimplementedGoofishHttpServiceServer) GetGoodsChangeSubscribeList(context.Context, *GoodsChangeSubscribeListRequest) (*GoodsChangeSubscribeListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsChangeSubscribeList not implemented")
}
func (UnimplementedGoofishHttpServiceServer) GoodsChangeSubscribe(context.Context, *GoodsChangeSubscribeRequest) (*GoodsChangeSubscribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsChangeSubscribe not implemented")
}
func (UnimplementedGoofishHttpServiceServer) GoodsChangeUnsubscribe(context.Context, *GoodsChangeUnsubscribeRequest) (*GoodsChangeUnsubscribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsChangeUnsubscribe not implemented")
}
func (UnimplementedGoofishHttpServiceServer) GoodsCallback(context.Context, *GoodsCallbackRequest) (*GoodsCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsCallback not implemented")
}
func (UnimplementedGoofishHttpServiceServer) CreateRechargeOrder(context.Context, *CreateRechargeOrderRequest) (*CreateRechargeOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRechargeOrder not implemented")
}
func (UnimplementedGoofishHttpServiceServer) CreateCardOrder(context.Context, *CreateCardOrderRequest) (*CreateCardOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCardOrder not implemented")
}
func (UnimplementedGoofishHttpServiceServer) GetOrderDetail(context.Context, *OrderDetailRequest) (*OrderDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderDetail not implemented")
}
func (UnimplementedGoofishHttpServiceServer) OrderCallback(context.Context, *OrderCallbackRequest) (*OrderCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderCallback not implemented")
}
func (UnimplementedGoofishHttpServiceServer) mustEmbedUnimplementedGoofishHttpServiceServer() {}
func (UnimplementedGoofishHttpServiceServer) testEmbeddedByValue()                            {}

// UnsafeGoofishHttpServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GoofishHttpServiceServer will
// result in compilation errors.
type UnsafeGoofishHttpServiceServer interface {
	mustEmbedUnimplementedGoofishHttpServiceServer()
}

func RegisterGoofishHttpServiceServer(s grpc.ServiceRegistrar, srv GoofishHttpServiceServer) {
	// If the following call pancis, it indicates UnimplementedGoofishHttpServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GoofishHttpService_ServiceDesc, srv)
}

func _GoofishHttpService_GetPlatformInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlatformInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).GetPlatformInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_GetPlatformInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).GetPlatformInfo(ctx, req.(*PlatformInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_GetUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).GetUserInfo(ctx, req.(*UserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_GetGoodsDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).GetGoodsDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_GetGoodsDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).GetGoodsDetail(ctx, req.(*GoodsDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_GetGoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).GetGoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_GetGoodsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).GetGoodsList(ctx, req.(*GoodsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_GetGoodsChangeSubscribeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsChangeSubscribeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).GetGoodsChangeSubscribeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_GetGoodsChangeSubscribeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).GetGoodsChangeSubscribeList(ctx, req.(*GoodsChangeSubscribeListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_GoodsChangeSubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsChangeSubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).GoodsChangeSubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_GoodsChangeSubscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).GoodsChangeSubscribe(ctx, req.(*GoodsChangeSubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_GoodsChangeUnsubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsChangeUnsubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).GoodsChangeUnsubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_GoodsChangeUnsubscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).GoodsChangeUnsubscribe(ctx, req.(*GoodsChangeUnsubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_GoodsCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).GoodsCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_GoodsCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).GoodsCallback(ctx, req.(*GoodsCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_CreateRechargeOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRechargeOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).CreateRechargeOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_CreateRechargeOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).CreateRechargeOrder(ctx, req.(*CreateRechargeOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_CreateCardOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCardOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).CreateCardOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_CreateCardOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).CreateCardOrder(ctx, req.(*CreateCardOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_GetOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).GetOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_GetOrderDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).GetOrderDetail(ctx, req.(*OrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoofishHttpService_OrderCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoofishHttpServiceServer).OrderCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GoofishHttpService_OrderCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoofishHttpServiceServer).OrderCallback(ctx, req.(*OrderCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GoofishHttpService_ServiceDesc is the grpc.ServiceDesc for GoofishHttpService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GoofishHttpService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "goofish.v1.GoofishHttpService",
	HandlerType: (*GoofishHttpServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPlatformInfo",
			Handler:    _GoofishHttpService_GetPlatformInfo_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _GoofishHttpService_GetUserInfo_Handler,
		},
		{
			MethodName: "GetGoodsDetail",
			Handler:    _GoofishHttpService_GetGoodsDetail_Handler,
		},
		{
			MethodName: "GetGoodsList",
			Handler:    _GoofishHttpService_GetGoodsList_Handler,
		},
		{
			MethodName: "GetGoodsChangeSubscribeList",
			Handler:    _GoofishHttpService_GetGoodsChangeSubscribeList_Handler,
		},
		{
			MethodName: "GoodsChangeSubscribe",
			Handler:    _GoofishHttpService_GoodsChangeSubscribe_Handler,
		},
		{
			MethodName: "GoodsChangeUnsubscribe",
			Handler:    _GoofishHttpService_GoodsChangeUnsubscribe_Handler,
		},
		{
			MethodName: "GoodsCallback",
			Handler:    _GoofishHttpService_GoodsCallback_Handler,
		},
		{
			MethodName: "CreateRechargeOrder",
			Handler:    _GoofishHttpService_CreateRechargeOrder_Handler,
		},
		{
			MethodName: "CreateCardOrder",
			Handler:    _GoofishHttpService_CreateCardOrder_Handler,
		},
		{
			MethodName: "GetOrderDetail",
			Handler:    _GoofishHttpService_GetOrderDetail_Handler,
		},
		{
			MethodName: "OrderCallback",
			Handler:    _GoofishHttpService_OrderCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "goofish/service/v1/goofish_http.proto",
}
