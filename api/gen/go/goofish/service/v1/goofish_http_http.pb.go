// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: goofish/service/v1/goofish_http.proto

package goofishv1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationGoofishHttpServiceCreateCardOrder = "/goofish.v1.GoofishHttpService/CreateCardOrder"
const OperationGoofishHttpServiceCreateRechargeOrder = "/goofish.v1.GoofishHttpService/CreateRechargeOrder"
const OperationGoofishHttpServiceGetGoodsChangeSubscribeList = "/goofish.v1.GoofishHttpService/GetGoodsChangeSubscribeList"
const OperationGoofishHttpServiceGetGoodsDetail = "/goofish.v1.GoofishHttpService/GetGoodsDetail"
const OperationGoofishHttpServiceGetGoodsList = "/goofish.v1.GoofishHttpService/GetGoodsList"
const OperationGoofishHttpServiceGetOrderDetail = "/goofish.v1.GoofishHttpService/GetOrderDetail"
const OperationGoofishHttpServiceGetPlatformInfo = "/goofish.v1.GoofishHttpService/GetPlatformInfo"
const OperationGoofishHttpServiceGetUserInfo = "/goofish.v1.GoofishHttpService/GetUserInfo"
const OperationGoofishHttpServiceGoodsCallback = "/goofish.v1.GoofishHttpService/GoodsCallback"
const OperationGoofishHttpServiceGoodsChangeSubscribe = "/goofish.v1.GoofishHttpService/GoodsChangeSubscribe"
const OperationGoofishHttpServiceGoodsChangeUnsubscribe = "/goofish.v1.GoofishHttpService/GoodsChangeUnsubscribe"
const OperationGoofishHttpServiceOrderCallback = "/goofish.v1.GoofishHttpService/OrderCallback"

type GoofishHttpServiceHTTPServer interface {
	// CreateCardOrder 创建卡密订单
	CreateCardOrder(context.Context, *CreateCardOrderRequest) (*CreateCardOrderResponse, error)
	// CreateRechargeOrder 创建直充订单
	CreateRechargeOrder(context.Context, *CreateRechargeOrderRequest) (*CreateRechargeOrderResponse, error)
	// GetGoodsChangeSubscribeList 查询商品订阅列表
	GetGoodsChangeSubscribeList(context.Context, *GoodsChangeSubscribeListRequest) (*GoodsChangeSubscribeListResponse, error)
	// GetGoodsDetail 查询商品详情
	GetGoodsDetail(context.Context, *GoodsDetailRequest) (*GoodsDetailResponse, error)
	// GetGoodsList 查询商品列表
	GetGoodsList(context.Context, *GoodsListRequest) (*GoodsListResponse, error)
	// GetOrderDetail 查询订单详情
	GetOrderDetail(context.Context, *OrderDetailRequest) (*OrderDetailResponse, error)
	// GetPlatformInfo 查询平台信息
	GetPlatformInfo(context.Context, *PlatformInfoRequest) (*PlatformInfoResponse, error)
	// GetUserInfo 查询商户信息
	GetUserInfo(context.Context, *UserInfoRequest) (*UserInfoResponse, error)
	// GoodsCallback 商品回调通知
	GoodsCallback(context.Context, *GoodsCallbackRequest) (*GoodsCallbackResponse, error)
	// GoodsChangeSubscribe 订阅商品变更通知
	GoodsChangeSubscribe(context.Context, *GoodsChangeSubscribeRequest) (*GoodsChangeSubscribeResponse, error)
	// GoodsChangeUnsubscribe 取消商品变更通知
	GoodsChangeUnsubscribe(context.Context, *GoodsChangeUnsubscribeRequest) (*GoodsChangeUnsubscribeResponse, error)
	// OrderCallback 订单回调通知
	OrderCallback(context.Context, *OrderCallbackRequest) (*OrderCallbackResponse, error)
}

func RegisterGoofishHttpServiceHTTPServer(s *http.Server, srv GoofishHttpServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/platform/info", _GoofishHttpService_GetPlatformInfo0_HTTP_Handler(srv))
	r.POST("/api/v1/user/info", _GoofishHttpService_GetUserInfo0_HTTP_Handler(srv))
	r.POST("/api/v1/goods/detail", _GoofishHttpService_GetGoodsDetail0_HTTP_Handler(srv))
	r.POST("/api/v1/goods/list", _GoofishHttpService_GetGoodsList0_HTTP_Handler(srv))
	r.POST("/api/v1/goods/change/subscribe/list", _GoofishHttpService_GetGoodsChangeSubscribeList0_HTTP_Handler(srv))
	r.POST("/api/v1/goods/change/subscribe", _GoofishHttpService_GoodsChangeSubscribe0_HTTP_Handler(srv))
	r.POST("/api/v1/goods/change/unsubscribe", _GoofishHttpService_GoodsChangeUnsubscribe0_HTTP_Handler(srv))
	r.POST("/api/v1/goods/callback", _GoofishHttpService_GoodsCallback0_HTTP_Handler(srv))
	r.POST("/api/v1/order/recharge", _GoofishHttpService_CreateRechargeOrder0_HTTP_Handler(srv))
	r.POST("/api/v1/order/card", _GoofishHttpService_CreateCardOrder0_HTTP_Handler(srv))
	r.POST("/api/v1/order/detail", _GoofishHttpService_GetOrderDetail0_HTTP_Handler(srv))
	r.POST("/api/v1/order/callback", _GoofishHttpService_OrderCallback0_HTTP_Handler(srv))
}

func _GoofishHttpService_GetPlatformInfo0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PlatformInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceGetPlatformInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPlatformInfo(ctx, req.(*PlatformInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PlatformInfoResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_GetUserInfo0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceGetUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserInfo(ctx, req.(*UserInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserInfoResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_GetGoodsDetail0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceGetGoodsDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsDetail(ctx, req.(*GoodsDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsDetailResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_GetGoodsList0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceGetGoodsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsList(ctx, req.(*GoodsListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsListResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_GetGoodsChangeSubscribeList0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsChangeSubscribeListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceGetGoodsChangeSubscribeList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsChangeSubscribeList(ctx, req.(*GoodsChangeSubscribeListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsChangeSubscribeListResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_GoodsChangeSubscribe0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsChangeSubscribeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceGoodsChangeSubscribe)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsChangeSubscribe(ctx, req.(*GoodsChangeSubscribeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsChangeSubscribeResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_GoodsChangeUnsubscribe0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsChangeUnsubscribeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceGoodsChangeUnsubscribe)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsChangeUnsubscribe(ctx, req.(*GoodsChangeUnsubscribeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsChangeUnsubscribeResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_GoodsCallback0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceGoodsCallback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsCallback(ctx, req.(*GoodsCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsCallbackResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_CreateRechargeOrder0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateRechargeOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceCreateRechargeOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateRechargeOrder(ctx, req.(*CreateRechargeOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateRechargeOrderResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_CreateCardOrder0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateCardOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceCreateCardOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateCardOrder(ctx, req.(*CreateCardOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateCardOrderResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_GetOrderDetail0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceGetOrderDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderDetail(ctx, req.(*OrderDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderDetailResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishHttpService_OrderCallback0_HTTP_Handler(srv GoofishHttpServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishHttpServiceOrderCallback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OrderCallback(ctx, req.(*OrderCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderCallbackResponse)
		return ctx.Result(200, reply)
	}
}

type GoofishHttpServiceHTTPClient interface {
	CreateCardOrder(ctx context.Context, req *CreateCardOrderRequest, opts ...http.CallOption) (rsp *CreateCardOrderResponse, err error)
	CreateRechargeOrder(ctx context.Context, req *CreateRechargeOrderRequest, opts ...http.CallOption) (rsp *CreateRechargeOrderResponse, err error)
	GetGoodsChangeSubscribeList(ctx context.Context, req *GoodsChangeSubscribeListRequest, opts ...http.CallOption) (rsp *GoodsChangeSubscribeListResponse, err error)
	GetGoodsDetail(ctx context.Context, req *GoodsDetailRequest, opts ...http.CallOption) (rsp *GoodsDetailResponse, err error)
	GetGoodsList(ctx context.Context, req *GoodsListRequest, opts ...http.CallOption) (rsp *GoodsListResponse, err error)
	GetOrderDetail(ctx context.Context, req *OrderDetailRequest, opts ...http.CallOption) (rsp *OrderDetailResponse, err error)
	GetPlatformInfo(ctx context.Context, req *PlatformInfoRequest, opts ...http.CallOption) (rsp *PlatformInfoResponse, err error)
	GetUserInfo(ctx context.Context, req *UserInfoRequest, opts ...http.CallOption) (rsp *UserInfoResponse, err error)
	GoodsCallback(ctx context.Context, req *GoodsCallbackRequest, opts ...http.CallOption) (rsp *GoodsCallbackResponse, err error)
	GoodsChangeSubscribe(ctx context.Context, req *GoodsChangeSubscribeRequest, opts ...http.CallOption) (rsp *GoodsChangeSubscribeResponse, err error)
	GoodsChangeUnsubscribe(ctx context.Context, req *GoodsChangeUnsubscribeRequest, opts ...http.CallOption) (rsp *GoodsChangeUnsubscribeResponse, err error)
	OrderCallback(ctx context.Context, req *OrderCallbackRequest, opts ...http.CallOption) (rsp *OrderCallbackResponse, err error)
}

type GoofishHttpServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewGoofishHttpServiceHTTPClient(client *http.Client) GoofishHttpServiceHTTPClient {
	return &GoofishHttpServiceHTTPClientImpl{client}
}

func (c *GoofishHttpServiceHTTPClientImpl) CreateCardOrder(ctx context.Context, in *CreateCardOrderRequest, opts ...http.CallOption) (*CreateCardOrderResponse, error) {
	var out CreateCardOrderResponse
	pattern := "/api/v1/order/card"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceCreateCardOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) CreateRechargeOrder(ctx context.Context, in *CreateRechargeOrderRequest, opts ...http.CallOption) (*CreateRechargeOrderResponse, error) {
	var out CreateRechargeOrderResponse
	pattern := "/api/v1/order/recharge"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceCreateRechargeOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) GetGoodsChangeSubscribeList(ctx context.Context, in *GoodsChangeSubscribeListRequest, opts ...http.CallOption) (*GoodsChangeSubscribeListResponse, error) {
	var out GoodsChangeSubscribeListResponse
	pattern := "/api/v1/goods/change/subscribe/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceGetGoodsChangeSubscribeList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) GetGoodsDetail(ctx context.Context, in *GoodsDetailRequest, opts ...http.CallOption) (*GoodsDetailResponse, error) {
	var out GoodsDetailResponse
	pattern := "/api/v1/goods/detail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceGetGoodsDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) GetGoodsList(ctx context.Context, in *GoodsListRequest, opts ...http.CallOption) (*GoodsListResponse, error) {
	var out GoodsListResponse
	pattern := "/api/v1/goods/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceGetGoodsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) GetOrderDetail(ctx context.Context, in *OrderDetailRequest, opts ...http.CallOption) (*OrderDetailResponse, error) {
	var out OrderDetailResponse
	pattern := "/api/v1/order/detail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceGetOrderDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) GetPlatformInfo(ctx context.Context, in *PlatformInfoRequest, opts ...http.CallOption) (*PlatformInfoResponse, error) {
	var out PlatformInfoResponse
	pattern := "/api/v1/platform/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceGetPlatformInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) GetUserInfo(ctx context.Context, in *UserInfoRequest, opts ...http.CallOption) (*UserInfoResponse, error) {
	var out UserInfoResponse
	pattern := "/api/v1/user/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceGetUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) GoodsCallback(ctx context.Context, in *GoodsCallbackRequest, opts ...http.CallOption) (*GoodsCallbackResponse, error) {
	var out GoodsCallbackResponse
	pattern := "/api/v1/goods/callback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceGoodsCallback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) GoodsChangeSubscribe(ctx context.Context, in *GoodsChangeSubscribeRequest, opts ...http.CallOption) (*GoodsChangeSubscribeResponse, error) {
	var out GoodsChangeSubscribeResponse
	pattern := "/api/v1/goods/change/subscribe"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceGoodsChangeSubscribe))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) GoodsChangeUnsubscribe(ctx context.Context, in *GoodsChangeUnsubscribeRequest, opts ...http.CallOption) (*GoodsChangeUnsubscribeResponse, error) {
	var out GoodsChangeUnsubscribeResponse
	pattern := "/api/v1/goods/change/unsubscribe"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceGoodsChangeUnsubscribe))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishHttpServiceHTTPClientImpl) OrderCallback(ctx context.Context, in *OrderCallbackRequest, opts ...http.CallOption) (*OrderCallbackResponse, error) {
	var out OrderCallbackResponse
	pattern := "/api/v1/order/callback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishHttpServiceOrderCallback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
