syntax = "proto3";

package goofish.service.v1;

import "errors/errors.proto";

// GoofishErrorReason 是 Goofish 服务错误定义
enum GoofishErrorReason {
    option (errors.default_code) = 500;

    // 400
    BAD_REQUEST = 0 [(errors.code) = 400]; // 错误请求
    INVALID_PARAMETER = 1 [(errors.code) = 400]; // 参数无效
    CODEC = 2 [(errors.code) = 400]; // 编解码器错误
    INVALID_CONTENT_TYPE = 3 [(errors.code) = 400]; // 无效的内容类型

    // 401
    UNAUTHORIZED = 100 [(errors.code) = 401]; // 未授权

    // 403
    FORBIDDEN = 300 [(errors.code) = 403]; // 禁止访问

    // 404
    NOT_FOUND = 400 [(errors.code) = 404]; // 找不到资源

    // 405
    METHOD_NOT_ALLOWED = 500 [(errors.code) = 405]; // 方法不允许

    // 500
    INTERNAL_SERVER_ERROR = 2000 [(errors.code) = 500]; // 内部服务器错误
    SERVICE_UNAVAILABLE = 2300 [(errors.code) = 503]; // 服务不可用
}
