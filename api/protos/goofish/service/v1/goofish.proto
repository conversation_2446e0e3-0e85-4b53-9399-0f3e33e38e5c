syntax = "proto3";
package goofish.v1;

// 查询平台信息
message PlatformInfoRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）
}
message PlatformInfoResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
  PlatformInfoData data = 3 [json_name = "data"];
}
message PlatformInfoData {
  int64 app_id = 1 [json_name = "app_id"];
}

// 查询商户信息
message UserInfoRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）
}
message UserInfoResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
  UserInfoData data = 3 [json_name = "data"];
}
message UserInfoData {
  int64 balance = 1 [json_name = "balance"];
}

// 商品详情模板
message GoodsTemplate {
  string code = 1 [json_name = "code"];
  string name = 2 [json_name = "name"];
  string desc = 3 [json_name = "desc"];
  int32 check = 4 [json_name = "check"];
}

// 商品详情
message GoodsDetail {
  string goods_no = 1 [json_name = "goods_no"];
  int32 goods_type = 2 [json_name = "goods_type"];
  string goods_name = 3 [json_name = "goods_name"];
  int64 price = 4 [json_name = "price"];
  int32 stock = 5 [json_name = "stock"];
  int32 status = 6 [json_name = "status"];
  int64 update_time = 7 [json_name = "update_time"];
  repeated GoodsTemplate template = 8 [json_name = "template"];
}

// 查询商品详情
message GoodsDetailRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）

  // Body 参数
  int32 goods_type = 4 [json_name = "goods_type"]; // 商品类型：1=直充商品，2=卡密商品
  string goods_no = 5 [json_name = "goods_no"]; // 商品编码
}
message GoodsDetailResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
  GoodsDetail data = 3 [json_name = "data"];
}

// 查询商品列表
message GoodsListRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）

  // Body 参数
  string keyword = 4 [json_name = "keyword"]; // 搜索关键词
  int32 goods_type = 5 [json_name = "goods_type"]; // 商品类型：1=直充商品，2=卡密商品
  int32 page_no = 6 [json_name = "page_no"]; // 当前页码
  int32 page_size = 7 [json_name = "page_size"]; // 每页行数
}
message GoodsListResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
  GoodsListData data = 3 [json_name = "data"];
}
message GoodsListData {
  repeated GoodsDetail list = 1 [json_name = "list"];
  int32 count = 2 [json_name = "count"];
}

// 订阅商品变更通知
message GoodsChangeSubscribeRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）

  // Body 参数
  int32 goods_type = 4 [json_name = "goods_type"]; // 商品类型：1=直充商品，2=卡密商品
  string goods_no = 5 [json_name = "goods_no"]; // 商品编码
  string token = 6 [json_name = "token"]; // 订阅标识
  string notify_url = 7 [json_name = "notify_url"]; // 回调地址
}
message GoodsChangeSubscribeResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
}

// 取消商品变更通知
message GoodsChangeUnsubscribeRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）

  // Body 参数
  int32 goods_type = 4 [json_name = "goods_type"]; // 商品类型：1=直充商品，2=卡密商品
  string goods_no = 5 [json_name = "goods_no"]; // 商品编码
  string token = 6 [json_name = "token"]; // 订阅标识
}
message GoodsChangeUnsubscribeResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
}

// 查询商品订阅列表 - Body 参数
message GoodsChangeSubscribeListBody {
  int32 goods_type = 1 [json_name = "goods_type"]; // 商品类型：1=直充商品，2=卡密商品
  string goods_no = 2 [json_name = "goods_no"]; // 商品编码
  int32 page_no = 3 [json_name = "page_no"]; // 当前页码
  int32 page_size = 4 [json_name = "page_size"]; // 每页行数
}

// 查询商品订阅列表 - 完整请求
message GoodsChangeSubscribeListRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）

  // Body 参数
  int32 goods_type = 4 [json_name = "goods_type"]; // 商品类型：1=直充商品，2=卡密商品
  string goods_no = 5 [json_name = "goods_no"]; // 商品编码
  int32 page_no = 6 [json_name = "page_no"]; // 当前页码
  int32 page_size = 7 [json_name = "page_size"]; // 每页行数
}
message GoodsChangeSubscribeListResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
  GoodsChangeSubscribeListData data = 3 [json_name = "data"];
}
message GoodsChangeSubscribeListData {
  repeated GoodsChangeSubscribeListItem list = 1 [json_name = "list"];
  int32 count = 2 [json_name = "count"];
}
message GoodsChangeSubscribeListItem {
  int32 goods_type = 1 [json_name = "goods_type"];
  string goods_no = 2 [json_name = "goods_no"];
  int64 subscribe_time = 3 [json_name = "subscribe_time"];
  string token = 4 [json_name = "token"];
  string notify_url = 5 [json_name = "notify_url"];
}

// 商品回调通知
message GoodsCallbackItem {
  string goods_no = 1 [json_name = "goods_no"];
  int32 goods_type = 2 [json_name = "goods_type"];
  int64 price = 3 [json_name = "price"];
  int32 stock = 4 [json_name = "stock"];
  int32 status = 5 [json_name = "status"];
  int64 change_time = 6 [json_name = "change_time"];
}
message GoodsCallbackRequest {
  string token = 1 [json_name = "token"]; // 回调令牌，用于验证回调请求的合法性
  repeated GoodsCallbackItem items = 2 [json_name = "items"];
}
message GoodsCallbackResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
}

// 订单相关
message BizContent {
  string account = 1 [json_name = "account"];
  string game_name = 2 [json_name = "game_name"];
  string game_role = 3 [json_name = "game_role"];
  string game_area = 4 [json_name = "game_area"];
  string game_server = 5 [json_name = "game_server"];
  string buyer_ip = 6 [json_name = "buyer_ip"];
  string buyer_area = 7 [json_name = "buyer_area"];
}
message CardItem {
  string card_no = 1 [json_name = "card_no"];
  string card_pwd = 2 [json_name = "card_pwd"];
}

// 创建直充订单
message CreateRechargeOrderRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）

  // Body 参数
  string order_no = 4 [json_name = "order_no"]; // 订单号
  string goods_no = 5 [json_name = "goods_no"]; // 商品编码
  BizContent biz_content = 6 [json_name = "biz_content"]; // 业务内容
  int32 buy_quantity = 7 [json_name = "buy_quantity"]; // 购买数量
  int64 max_amount = 8 [json_name = "max_amount"]; // 最大金额
  string notify_url = 9 [json_name = "notify_url"]; // 回调地址
  string biz_order_no = 10 [json_name = "biz_order_no"]; // 业务订单号
}
message CreateRechargeOrderResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
  RechargeOrderData data = 3 [json_name = "data"];
}
message RechargeOrderData {
  string order_no = 1 [json_name = "order_no"];
  string out_order_no = 2 [json_name = "out_order_no"];
  int32 order_status = 3 [json_name = "order_status"];
  int64 order_amount = 4 [json_name = "order_amount"];
  string goods_name = 5 [json_name = "goods_name"];
  int64 order_time = 6 [json_name = "order_time"];
  int64 end_time = 7 [json_name = "end_time"];
  string remark = 8 [json_name = "remark"];
}

// 创建卡密订单
message CreateCardOrderRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）

  // Body 参数
  string order_no = 4 [json_name = "order_no"]; // 订单号
  string goods_no = 5 [json_name = "goods_no"]; // 商品编码
  int32 buy_quantity = 6 [json_name = "buy_quantity"]; // 购买数量
  int64 max_amount = 7 [json_name = "max_amount"]; // 最大金额
  string notify_url = 8 [json_name = "notify_url"]; // 回调地址
  string biz_order_no = 9 [json_name = "biz_order_no"]; // 业务订单号
}
message CreateCardOrderResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
  CardOrderData data = 3 [json_name = "data"];
}
message CardOrderData {
  string order_no = 1 [json_name = "order_no"];
  string out_order_no = 2 [json_name = "out_order_no"];
  int32 order_status = 3 [json_name = "order_status"];
  int64 order_amount = 4 [json_name = "order_amount"];
  int64 order_time = 5 [json_name = "order_time"];
  int64 end_time = 6 [json_name = "end_time"];
  repeated CardItem card_items = 7 [json_name = "card_items"];
  string remark = 8 [json_name = "remark"];
}

// 查询订单详情
message OrderDetailRequest {
  // Query 参数
  string mch_id = 1 [json_name = "mch_id"]; // 货源平台商户ID（AppKey）
  int64 timestamp = 2 [json_name = "timestamp"]; // 当前时间戳（单位秒，5分钟内有效）
  string sign = 3 [json_name = "sign"]; // 签名MD5值（参考签名说明）

  // Body 参数
  int32 order_type = 4 [json_name = "order_type"]; // 订单类型
  string order_no = 5 [json_name = "order_no"]; // 订单号
  string out_order_no = 6 [json_name = "out_order_no"]; // 外部订单号
}
message OrderDetailResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
  OrderDetailData data = 3 [json_name = "data"];
}
message OrderDetailData {
  int32 order_type = 1 [json_name = "order_type"];
  string order_no = 2 [json_name = "order_no"];
  string out_order_no = 3 [json_name = "out_order_no"];
  int32 order_status = 4 [json_name = "order_status"];
  int64 order_amount = 5 [json_name = "order_amount"];
  string goods_no = 6 [json_name = "goods_no"];
  string goods_name = 7 [json_name = "goods_name"];
  int32 buy_quantity = 8 [json_name = "buy_quantity"];
  int64 order_time = 9 [json_name = "order_time"];
  int64 end_time = 10 [json_name = "end_time"];
  BizContent biz_content = 11 [json_name = "biz_content"];
  repeated CardItem card_items = 12 [json_name = "card_items"];
  string remark = 13 [json_name = "remark"];
}

// 订单回调通知
message OrderCallbackRequest {
  string token = 1 [json_name = "token"]; // 回调令牌，用于验证回调请求的合法性
  int32 order_type = 2 [json_name = "order_type"];
  string order_no = 3 [json_name = "order_no"];
  string out_order_no = 4 [json_name = "out_order_no"];
  int32 order_status = 5 [json_name = "order_status"];
  int64 end_time = 6 [json_name = "end_time"];
  repeated CardItem card_items = 7 [json_name = "card_items"];
  string remark = 8 [json_name = "remark"];
}
message OrderCallbackResponse {
  int32 code = 1 [json_name = "code"];
  string msg = 2 [json_name = "msg"];
}

// goofish API 服务定义
service GoofishService {
  // 查询平台信息
  rpc GetPlatformInfo(PlatformInfoRequest) returns (PlatformInfoResponse);
  // 查询商户信息
  rpc GetUserInfo(UserInfoRequest) returns (UserInfoResponse);
  // 查询商品详情
  rpc GetGoodsDetail(GoodsDetailRequest) returns (GoodsDetailResponse);
  // 查询商品列表
  rpc GetGoodsList(GoodsListRequest) returns (GoodsListResponse);
  // 查询商品订阅列表
  rpc GetGoodsChangeSubscribeList(GoodsChangeSubscribeListRequest) returns (GoodsChangeSubscribeListResponse);
  // 订阅商品变更通知
  rpc GoodsChangeSubscribe(GoodsChangeSubscribeRequest) returns (GoodsChangeSubscribeResponse);
  // 取消商品变更通知
  rpc GoodsChangeUnsubscribe(GoodsChangeUnsubscribeRequest) returns (GoodsChangeUnsubscribeResponse);
  // 商品回调通知
  rpc GoodsCallback(GoodsCallbackRequest) returns (GoodsCallbackResponse);
  // 创建直充订单
  rpc CreateRechargeOrder(CreateRechargeOrderRequest) returns (CreateRechargeOrderResponse);
  // 创建卡密订单
  rpc CreateCardOrder(CreateCardOrderRequest) returns (CreateCardOrderResponse);
  // 查询订单详情
  rpc GetOrderDetail(OrderDetailRequest) returns (OrderDetailResponse);
  // 订单回调通知
  rpc OrderCallback(OrderCallbackRequest) returns (OrderCallbackResponse);
}
