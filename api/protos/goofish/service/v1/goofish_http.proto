syntax = "proto3";

package goofish.v1;

import "goofish/service/v1/goofish.proto";
import "google/api/annotations.proto";

option go_package = "kratos-admin/api/gen/go/goofish/service/v1;goofishv1";

// goofish HTTP API 服务定义
service GoofishHttpService {
  // 查询平台信息
  rpc GetPlatformInfo(PlatformInfoRequest) returns (PlatformInfoResponse) {
    option (google.api.http) = {
      post: "/api/v1/platform/info"
      body: "*"
    };
  }
  
  // 查询商户信息
  rpc GetUserInfo(UserInfoRequest) returns (UserInfoResponse) {
    option (google.api.http) = {
      post: "/api/v1/user/info"
      body: "*"
    };
  }
  
  // 查询商品详情
  rpc GetGoodsDetail(GoodsDetailRequest) returns (GoodsDetailResponse) {
    option (google.api.http) = {
      post: "/api/v1/goods/detail"
      body: "*"
    };
  }
  
  // 查询商品列表
  rpc GetGoodsList(GoodsListRequest) returns (GoodsListResponse) {
    option (google.api.http) = {
      post: "/api/v1/goods/list"
      body: "*"
    };
  }
  
  // 查询商品订阅列表
  rpc GetGoodsChangeSubscribeList(GoodsChangeSubscribeListRequest) returns (GoodsChangeSubscribeListResponse) {
    option (google.api.http) = {
      post: "/api/v1/goods/change/subscribe/list"
      body: "*"
    };
  }
  
  // 订阅商品变更通知
  rpc GoodsChangeSubscribe(GoodsChangeSubscribeRequest) returns (GoodsChangeSubscribeResponse) {
    option (google.api.http) = {
      post: "/api/v1/goods/change/subscribe"
      body: "*"
    };
  }
  
  // 取消商品变更通知
  rpc GoodsChangeUnsubscribe(GoodsChangeUnsubscribeRequest) returns (GoodsChangeUnsubscribeResponse) {
    option (google.api.http) = {
      post: "/api/v1/goods/change/unsubscribe"
      body: "*"
    };
  }
  
  // 商品回调通知
  rpc GoodsCallback(GoodsCallbackRequest) returns (GoodsCallbackResponse) {
    option (google.api.http) = {
      post: "/api/v1/goods/callback"
      body: "*"
    };
  }
  
  // 创建直充订单
  rpc CreateRechargeOrder(CreateRechargeOrderRequest) returns (CreateRechargeOrderResponse) {
    option (google.api.http) = {
      post: "/api/v1/order/recharge"
      body: "*"
    };
  }
  
  // 创建卡密订单
  rpc CreateCardOrder(CreateCardOrderRequest) returns (CreateCardOrderResponse) {
    option (google.api.http) = {
      post: "/api/v1/order/card"
      body: "*"
    };
  }
  
  // 查询订单详情
  rpc GetOrderDetail(OrderDetailRequest) returns (OrderDetailResponse) {
    option (google.api.http) = {
      post: "/api/v1/order/detail"
      body: "*"
    };
  }
  
  // 订单回调通知
  rpc OrderCallback(OrderCallbackRequest) returns (OrderCallbackResponse) {
    option (google.api.http) = {
      post: "/api/v1/order/callback"
      body: "*"
    };
  }
}
